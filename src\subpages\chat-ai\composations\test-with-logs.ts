import { parseStreamingData, resetStreamingParser } from "./useParseStreaming";

// 测试场景三，查看详细日志
console.log("=== 测试场景三（带详细日志） ===");

resetStreamingParser();

const json1 = '{"type":"start","content":"开始"}';
const json2 = '{"type":"middle","content":"中间"}';
const json3 = '{"type":"end","content":"结束"}';

console.log("JSON1:", json1, "长度:", json1.length);
console.log("JSON2:", json2);
console.log("JSON3:", json3);

// 第一次：data: + JSON开始部分
const chunk1 = "data: " + json1.substring(0, 15); // "data: {"type":"sta"
console.log("\n=== 第一次调用 ===");
console.log("Chunk1:", `"${chunk1}"`);

const result1 = parseStreamingData(chunk1);
console.log("Result1 长度:", result1.length);

// 第二次：JSON中间部分
const chunk2 = json1.substring(15, 25); // "rt","cont"
console.log("\n=== 第二次调用 ===");
console.log("Chunk2:", `"${chunk2}"`);

const result2 = parseStreamingData(chunk2);
console.log("Result2 长度:", result2.length);

// 第三次：JSON结尾 + 多个完整data块
const chunk3 = json1.substring(25) + `\ndata: ${json2}\ndata: ${json3}`; // "ent":"开始"} + data: {...} + data: {...}
console.log("\n=== 第三次调用 ===");
console.log("Chunk3:", `"${chunk3}"`);

const result3 = parseStreamingData(chunk3);
console.log("Result3 长度:", result3.length);

console.log("\n=== 最终结果 ===");
console.log("期望: 0, 0, 3");
console.log("实际:", result1.length, result2.length, result3.length);

if (result3.length === 3) {
  console.log("✅ 测试通过");
} else {
  console.log("❌ 测试失败");
  console.log("Result3 详细:", result3);
}
