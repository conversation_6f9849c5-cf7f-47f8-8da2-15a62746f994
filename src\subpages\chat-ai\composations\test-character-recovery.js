// 测试字符截断恢复功能

// 模拟字节级截断和恢复
function testCharacterRecovery() {
  console.log("=== 测试字符截断恢复 ===");
  
  // 测试数据：包含中文字符的JSON
  const testJson = '{"message": "这是测试消息"}';
  console.log("原始JSON:", testJson);
  
  const bytes = new TextEncoder().encode(testJson);
  console.log("总字节数:", bytes.length);
  
  // 在中文字符"消"的中间截断（"消"字符通常占3个字节）
  // 找到"消"字符的位置
  const messageIndex = testJson.indexOf("消");
  console.log("'消'字符在字符串中的位置:", messageIndex);
  
  // 将字符串转换为字节，然后在"消"字符的第2个字节处截断
  const beforeChar = testJson.substring(0, messageIndex);
  const beforeBytes = new TextEncoder().encode(beforeChar);
  const charBytes = new TextEncoder().encode("消");
  
  console.log("'消'字符的字节:", Array.from(charBytes));
  
  // 在"消"字符的第2个字节处截断
  const cutPos = beforeBytes.length + 2; // 截断"消"字符的第2个字节
  
  const chunk1Bytes = bytes.slice(0, cutPos);
  const chunk2Bytes = bytes.slice(cutPos);
  
  console.log("Chunk1 字节数:", chunk1Bytes.length);
  console.log("Chunk2 字节数:", chunk2Bytes.length);
  
  // 尝试解码截断的字节
  const chunk1Str = new TextDecoder("utf-8", { fatal: false }).decode(chunk1Bytes);
  const chunk2Str = new TextDecoder("utf-8", { fatal: false }).decode(chunk2Bytes);
  
  console.log("Chunk1 解码结果:", `"${chunk1Str}"`);
  console.log("Chunk1 包含替换字符:", chunk1Str.includes('\uFFFD'));
  console.log("Chunk2 解码结果:", `"${chunk2Str}"`);
  console.log("Chunk2 包含替换字符:", chunk2Str.includes('\uFFFD'));
  
  // 测试字节级安全解码
  console.log("\n=== 测试字节级安全解码 ===");
  
  function safeDecodeBytes(bytes) {
    try {
      const decoded = new TextDecoder("utf-8", { fatal: true }).decode(bytes);
      return { decoded, remainingBytes: new Uint8Array(0) };
    } catch {
      // 解码失败，从末尾开始尝试找到安全的解码位置
      for (let i = bytes.length - 1; i >= Math.max(0, bytes.length - 4); i--) {
        try {
          const safeBytes = bytes.slice(0, i);
          const decoded = new TextDecoder("utf-8", { fatal: true }).decode(safeBytes);
          const remainingBytes = bytes.slice(i);
          console.log("找到安全解码位置:", i, "剩余字节:", remainingBytes.length);
          return { decoded, remainingBytes };
        } catch {
          continue;
        }
      }
      
      console.warn("无法安全解码字节数组");
      return { decoded: "", remainingBytes: bytes };
    }
  }
  
  // 测试第一个chunk的安全解码
  const result1 = safeDecodeBytes(chunk1Bytes);
  console.log("Chunk1 安全解码:", `"${result1.decoded}"`);
  console.log("Chunk1 剩余字节:", result1.remainingBytes.length);
  
  // 合并剩余字节和第二个chunk
  const combinedBytes = new Uint8Array(result1.remainingBytes.length + chunk2Bytes.length);
  combinedBytes.set(result1.remainingBytes);
  combinedBytes.set(chunk2Bytes, result1.remainingBytes.length);
  
  const result2 = safeDecodeBytes(combinedBytes);
  console.log("合并后解码:", `"${result2.decoded}"`);
  
  // 验证恢复的完整性
  const recovered = result1.decoded + result2.decoded;
  console.log("恢复的完整字符串:", `"${recovered}"`);
  console.log("与原始字符串一致:", recovered === testJson);
  
  return recovered === testJson;
}

// 测试实际的流式数据场景
function testStreamingWithCharacterTruncation() {
  console.log("\n=== 测试流式数据字符截断场景 ===");
  
  const json1 = '{"content": "消息一"}';
  const json2 = '{"content": "消息二"}';
  
  const streamData = `data: ${json1}\ndata: ${json2}`;
  console.log("流式数据:", streamData);
  
  const bytes = new TextEncoder().encode(streamData);
  
  // 在第一个JSON的"消"字符中间截断
  const firstJsonStart = streamData.indexOf(json1);
  const firstMsgIndex = streamData.indexOf("消息一");
  const cutPos = new TextEncoder().encode(streamData.substring(0, firstMsgIndex + 1)).length - 1; // 在"消"字符中间
  
  const chunk1Bytes = bytes.slice(0, cutPos);
  const chunk2Bytes = bytes.slice(cutPos);
  
  const chunk1 = new TextDecoder("utf-8", { fatal: false }).decode(chunk1Bytes);
  const chunk2 = new TextDecoder("utf-8", { fatal: false }).decode(chunk2Bytes);
  
  console.log("Chunk1:", `"${chunk1}"`);
  console.log("Chunk1 包含替换字符:", chunk1.includes('\uFFFD'));
  console.log("Chunk2:", `"${chunk2}"`);
  console.log("Chunk2 包含替换字符:", chunk2.includes('\uFFFD'));
  
  // 如果有替换字符，说明字符被截断了
  if (chunk1.includes('\uFFFD') || chunk2.includes('\uFFFD')) {
    console.log("✅ 成功模拟了字符截断情况");
    console.log("这种情况下，字节级处理应该能够正确恢复字符");
  } else {
    console.log("❌ 未能模拟字符截断，可能截断位置不在字符边界");
  }
}

const test1 = testCharacterRecovery();
testStreamingWithCharacterTruncation();

console.log("\n=== 总结 ===");
console.log("字符恢复测试:", test1 ? "✅ 通过" : "❌ 失败");

if (test1) {
  console.log("\n🎉 字节级处理逻辑能够正确处理字符截断问题！");
  console.log("现在的 useParseStreaming.ts 应该能够解决你遇到的场景三问题。");
} else {
  console.log("\n❌ 字符恢复测试失败，需要进一步调试。");
}
