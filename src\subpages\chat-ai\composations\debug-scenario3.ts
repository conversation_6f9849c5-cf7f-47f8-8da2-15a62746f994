import { parseStreamingData, resetStreamingParser } from "./useParseStreaming";

// 调试场景三：第三次返回多个完整JSON
console.log("=== 调试场景三 ===");
resetStreamingParser();

const testData1 = { type: "start", content: "开始消息" };
const testData2 = { type: "middle", content: "中间消息" };
const testData3 = { type: "end", content: "结束消息" };

const json1 = JSON.stringify(testData1);
const json2 = JSON.stringify(testData2);
const json3 = JSON.stringify(testData3);

console.log("JSON1:", json1);
console.log("JSON2:", json2);
console.log("JSON3:", json3);

// 模拟场景三
// 第一次：data: + 被截断的JSON开始
const chunk1 = "data: " + json1.substring(0, 20);
console.log("\nChunk1:", `"${chunk1}"`);

const result1 = parseStreamingData(chunk1);
console.log("Result1 长度:", result1.length);
console.log("Result1:", result1);

// 第二次：JSON中间部分（无data:前缀）
const chunk2 = json1.substring(20, 35);
console.log("\nChunk2:", `"${chunk2}"`);

const result2 = parseStreamingData(chunk2);
console.log("Result2 长度:", result2.length);
console.log("Result2:", result2);

// 第三次：JSON结尾 + 多个完整的data块
const chunk3 = json1.substring(35) + `\ndata: ${json2}\ndata: ${json3}`;
console.log("\nChunk3:", `"${chunk3}"`);

const result3 = parseStreamingData(chunk3);
console.log("Result3 长度:", result3.length);
console.log("Result3:", result3);

// 验证结果
console.log("\n=== 验证结果 ===");
console.log("期望: result1=0, result2=0, result3=3");
console.log("实际: result1=" + result1.length + ", result2=" + result2.length + ", result3=" + result3.length);

if (result1.length === 0 && result2.length === 0 && result3.length === 3) {
  console.log("✅ 场景三测试通过！");
} else {
  console.log("❌ 场景三测试失败！");
  
  // 详细分析缓存状态
  console.log("\n=== 详细分析 ===");
  resetStreamingParser();
  
  console.log("重新执行，查看缓存状态...");
  
  // 模拟内部缓存状态
  let mockBuffer = "";
  
  mockBuffer += chunk1;
  console.log("After chunk1, buffer:", `"${mockBuffer}"`);
  
  mockBuffer += chunk2;
  console.log("After chunk2, buffer:", `"${mockBuffer}"`);
  
  mockBuffer += chunk3;
  console.log("After chunk3, buffer:", `"${mockBuffer}"`);
  
  // 检查第三次的缓存是否以data:开头
  console.log("第三次缓存是否以data:开头:", mockBuffer.trim().startsWith("data:"));
}
