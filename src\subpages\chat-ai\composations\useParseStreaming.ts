import type { ChunkData } from "@/api/servers/chat/type";

// 全局缓存，用于存储未完成的数据
let incompleteBuffer = "";

// 移除不再需要的函数，简化代码

/**
 * 去除流式数据中的"data:"前缀，并将剩余部分解析为JSON对象数组。
 * 简化版本：不考虑data:前缀被截断的情况
 *
 * 流式数据格式规则：
 * 1. 每个数据块格式：data: {JSON对象}
 * 2. 如果JSON被截断，后续chunk只包含JSON剩余部分，不重复data:前缀
 * 3. 只有当前数据块完全传输完成后，才开始新的data:块
 *
 * 支持的截断情况：
 * - JSON对象在任意位置被截断
 * - 一次返回多个完整data块
 * - 截断JSON完成后跟随多个完整data块
 *
 * @param data - 流式数据chunk
 * @returns 解析后的JSON对象数组
 */
export function parseStreamingData(data: string): ChunkData[] {
  const parsedData: ChunkData[] = [];

  // 检测新数据是否在字符边界被截断
  const truncatePos = detectTruncatedCharacter(data);
  if (truncatePos > 0) {
    // 只处理安全部分，剩余部分等待下次
    const safeData = data.substring(0, truncatePos);
    const remainingData = data.substring(truncatePos);

    console.log("检测到字符截断，分割处理");
    console.log("安全数据:", safeData);
    console.log("剩余数据:", remainingData);

    // 先处理安全部分
    incompleteBuffer += safeData;

    // 处理缓存中的数据
    const processResult = processStreamData();
    parsedData.push(...processResult.parsedData);

    // 更新缓存，加入剩余数据
    incompleteBuffer = processResult.remainingBuffer + remainingData;
  } else {
    // 将新数据追加到缓存中
    incompleteBuffer += data;

    console.log("当前缓存内容:", incompleteBuffer);

    // 处理缓存中的数据
    const processResult = processStreamData();
    parsedData.push(...processResult.parsedData);

    // 更新缓存
    incompleteBuffer = processResult.remainingBuffer;

    console.log("解析到的数据条数:", parsedData.length);
    console.log("剩余缓存长度:", incompleteBuffer.length);
    console.log("剩余缓存内容:", incompleteBuffer);
  }

  return parsedData;
}

/**
 * 检测字符串是否在多字节字符边界被截断
 * @param str 要检查的字符串
 * @returns 如果被截断返回截断位置，否则返回-1
 */
function detectTruncatedCharacter(str: string): number {
  if (!str) return -1;

  // 检查字符串末尾是否有替换字符（�），这通常表示字符被截断
  if (str.includes("\uFFFD")) {
    console.log("检测到替换字符，可能存在字符截断");

    // 从末尾开始查找安全位置（不包含替换字符的位置）
    for (let i = str.length - 1; i >= 0; i--) {
      if (str[i] === "\uFFFD") {
        // 找到第一个替换字符，返回其前面的位置
        console.log("检测到字符截断，安全位置:", i, "原长度:", str.length);
        return i;
      }
    }
  }

  // 检查字符串末尾几个字符是否可能是不完整的多字节字符
  const lastFewChars = str.slice(-4);
  for (let i = lastFewChars.length - 1; i >= 0; i--) {
    const char = lastFewChars[i];
    const charCode = char.charCodeAt(0);

    // 检查是否是不完整的UTF-8序列的开始
    // 0xD800-0xDBFF 是高代理项，0xDC00-0xDFFF 是低代理项
    if (charCode >= 0xd800 && charCode <= 0xdbff) {
      // 高代理项后面应该跟低代理项
      const nextChar = lastFewChars[i + 1];
      if (
        !nextChar ||
        nextChar.charCodeAt(0) < 0xdc00 ||
        nextChar.charCodeAt(0) > 0xdfff
      ) {
        const truncatePos = str.length - (lastFewChars.length - i);
        console.log(
          "检测到不完整的代理对，安全位置:",
          truncatePos,
          "原长度:",
          str.length,
        );
        return truncatePos;
      }
    }
  }

  return -1;
}

/**
 * 安全地解析JSON字符串
 */
function tryParseJson(jsonStr: string): ChunkData | null {
  try {
    const parsed = JSON.parse(jsonStr);
    if (typeof parsed === "object" && parsed !== null) {
      return parsed as ChunkData;
    }
  } catch (error) {
    console.warn(
      `无法解析JSON字符串: ${jsonStr.substring(0, 100)}...。错误: ${
        (error as Error).message
      }`,
    );
  }
  return null;
}

/**
 * 处理流式数据，简化版本
 *
 * 处理场景：
 * 1. 第一次：data: {被截断JSON开始}
 * 2. 第二次：{JSON中间部分} (无data:前缀)
 * 3. 第三次：{JSON结尾} + data: {完整JSON2} + data: {完整JSON3}...
 *
 * 核心逻辑：
 * - 优先尝试解析续传的JSON（无data:前缀）
 * - 然后循环解析所有完整的data:块
 */
function processStreamData(): {
  parsedData: ChunkData[];
  remainingBuffer: string;
} {
  const parsedData: ChunkData[] = [];
  const buffer = incompleteBuffer;
  let processedLength = 0;

  // 第一步：处理可能的JSON续传数据（无data:前缀）
  if (!buffer.trim().startsWith("data:")) {
    const result = tryParseJsonFromStart(buffer.trim());
    if (result.jsonStr) {
      // 找到完整的续传JSON
      const parsed = tryParseJson(result.jsonStr);
      if (parsed) {
        parsedData.push(parsed);
      }

      // 计算处理的长度（包括前面的空白字符）
      const trimmedStart = buffer.length - buffer.trim().length;
      processedLength = trimmedStart + result.jsonStr.length;

      // 跳过JSON后的空白字符和换行符
      while (
        processedLength < buffer.length &&
        /\s/.test(buffer[processedLength])
      ) {
        processedLength++;
      }
    } else if (result.isIncomplete) {
      // JSON不完整，等待更多数据
      return {
        parsedData,
        remainingBuffer: buffer,
      };
    } else {
      // 不是有效的JSON，跳过到第一个data:或结束
      const firstDataIndex = buffer.indexOf("data:");
      if (firstDataIndex !== -1) {
        processedLength = firstDataIndex;
      } else {
        // 没有data:，全部丢弃
        return {
          parsedData,
          remainingBuffer: "",
        };
      }
    }
  }

  // 第二步：循环处理所有完整的data:块
  while (processedLength < buffer.length) {
    const remainingBuffer = buffer.substring(processedLength);

    // 查找下一个data:
    const dataMatch = remainingBuffer.match(/^\s*data:\s*/);
    if (!dataMatch) {
      // 没有找到data:，结束处理
      break;
    }

    // 找到data:，定位JSON开始位置
    const dataPrefix = dataMatch[0];
    const jsonStart = processedLength + dataPrefix.length;

    if (jsonStart >= buffer.length) {
      // data:后面没有内容，等待更多数据
      break;
    }

    if (buffer[jsonStart] !== "{") {
      // data:后面不是JSON开始，可能还在等待JSON数据
      break;
    }

    // 尝试提取完整的JSON对象
    const result = extractCompleteJsonFromPosition(buffer, jsonStart);

    if (result.jsonStr) {
      // 找到完整的JSON
      const parsed = tryParseJson(result.jsonStr);
      if (parsed) {
        parsedData.push(parsed);
      }
      processedLength = jsonStart + result.jsonStr.length;

      // 跳过JSON后的空白字符和换行符
      while (
        processedLength < buffer.length &&
        /\s/.test(buffer[processedLength])
      ) {
        processedLength++;
      }
    } else if (result.isIncomplete) {
      // JSON不完整，等待更多数据
      break;
    } else {
      // JSON格式错误，跳过这个data:
      processedLength = jsonStart;
    }
  }

  return {
    parsedData,
    remainingBuffer: buffer.substring(processedLength),
  };
}

/**
 * 尝试从字符串开始位置解析JSON对象（用于处理没有data:前缀的续传数据）
 * 返回解析结果，包含JSON字符串和是否完整的标识
 */
function tryParseJsonFromStart(data: string): {
  jsonStr: string | null;
  isIncomplete: boolean;
} {
  const trimmedData = data.trim();
  if (!trimmedData.startsWith("{")) {
    return { jsonStr: null, isIncomplete: false };
  }

  return extractCompleteJsonFromPosition(trimmedData, 0);
}

/**
 * 从指定位置开始提取完整的JSON对象字符串
 * 返回解析结果，包含JSON字符串和是否完整的标识
 */
function extractCompleteJsonFromPosition(
  data: string,
  startIndex: number,
): {
  jsonStr: string | null;
  isIncomplete: boolean;
} {
  const len = data.length;
  if (startIndex >= len || data[startIndex] !== "{") {
    return { jsonStr: null, isIncomplete: false };
  }

  // 动态规划状态定义
  const dp: Array<{
    inString: boolean;
    escapeNext: boolean;
    braceCount: number;
    isValid: boolean;
  }> = Array.from({ length: len - startIndex });

  // 初始状态
  dp[0] = {
    inString: false,
    escapeNext: false,
    braceCount: 1, // 第一个字符是 '{'
    isValid: true,
  };

  // 动态规划转移
  for (let i = 1; i < len - startIndex; i++) {
    const char = data[startIndex + i];
    const prev = dp[i - 1];

    // 如果前一个状态无效，当前状态也无效
    if (!prev.isValid) {
      dp[i] = { ...prev, isValid: false };
      continue;
    }

    const currentState = {
      inString: prev.inString,
      escapeNext: false,
      braceCount: prev.braceCount,
      isValid: true,
    };

    // 处理转义字符
    if (prev.escapeNext) {
      // 前一个字符是转义字符，当前字符被转义
      dp[i] = currentState;
      continue;
    }

    if (char === "\\") {
      currentState.escapeNext = true;
    } else if (char === '"') {
      // 引号切换字符串状态
      currentState.inString = !prev.inString;
    } else if (!prev.inString) {
      // 不在字符串内部，处理括号
      if (char === "{") {
        currentState.braceCount++;
      } else if (char === "}") {
        currentState.braceCount--;

        // 找到完整的JSON对象
        if (currentState.braceCount === 0) {
          return {
            jsonStr: data.substring(startIndex, startIndex + i + 1),
            isIncomplete: false,
          };
        }

        // 括号不匹配，状态无效
        if (currentState.braceCount < 0) {
          currentState.isValid = false;
        }
      }
    }

    dp[i] = currentState;
  }

  // 没有找到完整的JSON对象，可能是数据被截断
  const lastState = dp[dp.length - 1];
  const isIncomplete =
    lastState && lastState.isValid && lastState.braceCount > 0;

  return { jsonStr: null, isIncomplete };
}

/**
 * 重置解析器的内部缓存
 */
export function resetStreamingParser(): void {
  incompleteBuffer = "";
}
