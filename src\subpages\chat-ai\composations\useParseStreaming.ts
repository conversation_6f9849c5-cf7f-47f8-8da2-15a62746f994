import type { ChunkData } from "@/api/servers/chat/type";

// 全局缓存，用于存储未完成的数据
let incompleteBuffer = "";

/**
 * 检测数据块是否完整
 * 优化版本：更精确地处理各种截断情况
 */
function isDataComplete(buffer: string): boolean {
  if (!buffer.trim()) return true;

  // 首先检查是否有不完整的 "data:" 前缀
  if (
    buffer.endsWith("d") ||
    buffer.endsWith("da") ||
    buffer.endsWith("dat") ||
    buffer.endsWith("data") ||
    buffer.endsWith("data:")
  ) {
    return false;
  }

  // 查找所有 "data:" 块的位置
  const dataPositions = findAllDataPositions(buffer);

  if (dataPositions.length === 0) {
    // 没有 "data:" 前缀，可能是续传的JSON数据
    return isJsonComplete(buffer.trim());
  }

  // 检查最后一个 "data:" 块是否完整
  const lastDataPos = dataPositions[dataPositions.length - 1];
  const lastBlock = buffer.substring(lastDataPos + 5).trim(); // 跳过 "data:"

  if (!lastBlock) {
    // "data:" 后面没有内容，肯定不完整
    return false;
  }

  if (!lastBlock.startsWith("{")) {
    // JSON 还没开始，不完整
    return false;
  }

  // 检查最后一个JSON对象是否完整
  return isJsonComplete(lastBlock);
}

/**
 * 查找所有 "data:" 的位置
 */
function findAllDataPositions(buffer: string): number[] {
  const positions: number[] = [];
  let index = 0;

  while (index < buffer.length) {
    const pos = buffer.indexOf("data:", index);
    if (pos === -1) break;
    positions.push(pos);
    index = pos + 5;
  }

  return positions;
}

/**
 * 检查JSON字符串是否完整
 * 更精确的JSON完整性检测
 */
function isJsonComplete(jsonStr: string): boolean {
  if (!jsonStr.trim()) return true;
  if (!jsonStr.trim().startsWith("{")) return false;

  let braceCount = 0;
  let inString = false;
  let escapeNext = false;

  for (let i = 0; i < jsonStr.length; i++) {
    const char = jsonStr[i];

    if (escapeNext) {
      escapeNext = false;
      continue;
    }

    if (char === "\\") {
      escapeNext = true;
      continue;
    }

    if (char === '"') {
      inString = !inString;
      continue;
    }

    if (!inString) {
      if (char === "{") {
        braceCount++;
      } else if (char === "}") {
        braceCount--;
        // 如果括号匹配完成，说明JSON完整
        if (braceCount === 0) {
          return true;
        }
      }
    }
  }

  // 如果还在字符串中或者有未闭合的括号，说明不完整
  return false;
}

/**
 * 去除流式数据中的"data:"前缀，并将剩余部分解析为JSON对象数组。
 * 优化版本：更好地处理各种数据截断情况
 *
 * 支持的截断情况：
 * 1. "data:" 前缀被截断 (如: "d", "da", "dat", "data", "data:")
 * 2. JSON对象被截断在任意位置
 * 3. 多个data块混合截断
 * 4. 跨chunk的JSON续传数据
 *
 * @param data - 包含一个或多个JSON对象的输入字符串，每个JSON对象以"data:"开头
 * @returns 解析后的JSON对象数组
 */
export function parseStreamingData(data: string): ChunkData[] {
  const parsedData: ChunkData[] = [];

  // 将新数据追加到缓存中，逐步构建完整的数据流
  incompleteBuffer += data;

  console.log(
    "当前缓存内容:",
    incompleteBuffer.substring(0, 200) +
      (incompleteBuffer.length > 200 ? "..." : ""),
  );

  // 处理缓存中的数据，提取所有完整的数据块
  const processResult = processBufferedData();
  parsedData.push(...processResult.parsedData);

  // 更新缓存，移除已处理的数据
  incompleteBuffer = processResult.remainingBuffer;

  // 如果数据看起来已经完整（没有不完整的data:前缀且没有不完整的JSON），清理缓存
  if (shouldClearBuffer(incompleteBuffer)) {
    console.log("数据流看起来已完整，清理缓存");
    resetStreamingParser();
  }

  console.log("解析到的数据条数:", parsedData.length);
  console.log("剩余缓存长度:", incompleteBuffer.length);

  return parsedData;
}

/**
 * 检查字符串是否是不完整的"data:"前缀
 */
function isIncompleteDataPrefix(str: string): boolean {
  const trimmed = str.trim();
  return (
    trimmed === "d" ||
    trimmed === "da" ||
    trimmed === "dat" ||
    trimmed === "data" ||
    trimmed === "data:"
  );
}

/**
 * 查找JSON开始位置
 */
function findJsonStart(buffer: string, startPos: number): number {
  let pos = startPos;
  // 跳过空白字符
  while (pos < buffer.length && /\s/.test(buffer[pos])) {
    pos++;
  }

  // 检查是否找到JSON开始标记
  if (pos < buffer.length && buffer[pos] === "{") {
    return pos;
  }

  return -1;
}

/**
 * 安全地解析JSON字符串
 */
function tryParseJson(jsonStr: string): ChunkData | null {
  try {
    const parsed = JSON.parse(jsonStr);
    if (typeof parsed === "object" && parsed !== null) {
      return parsed as ChunkData;
    }
  } catch (error) {
    console.warn(
      `无法解析JSON字符串: ${jsonStr.substring(0, 100)}...。错误: ${
        (error as Error).message
      }`,
    );
  }
  return null;
}

/**
 * 判断是否应该清理缓存
 * 当缓存中只有空白字符或无意义的数据时，可以清理
 */
function shouldClearBuffer(buffer: string): boolean {
  const trimmed = buffer.trim();

  // 如果缓存为空或只有空白字符，可以清理
  if (!trimmed) {
    return true;
  }

  // 如果缓存中没有"data:"相关内容且不是JSON，可以清理
  if (!trimmed.includes("data") && !trimmed.startsWith("{")) {
    return true;
  }

  // 如果缓存很小且不包含有意义的内容，可以清理
  if (
    trimmed.length < 10 &&
    !isIncompleteDataPrefix(trimmed) &&
    !trimmed.startsWith("{")
  ) {
    return true;
  }

  return false;
}

/**
 * 处理缓存中的数据，提取所有完整的数据块
 */
function processBufferedData(): {
  parsedData: ChunkData[];
  remainingBuffer: string;
} {
  const parsedData: ChunkData[] = [];
  const buffer = incompleteBuffer;
  let processedLength = 0;

  while (processedLength < buffer.length) {
    // 情况1: 处理没有"data:"前缀的续传JSON数据
    if (processedLength === 0 && !buffer.trim().startsWith("data:")) {
      const result = tryParseJsonFromStart(buffer);
      if (result.jsonStr) {
        const parsed = tryParseJson(result.jsonStr);
        if (parsed) {
          parsedData.push(parsed);
        }
        processedLength = result.jsonStr.length;
        // 跳过JSON后的空白字符
        while (
          processedLength < buffer.length &&
          /\s/.test(buffer[processedLength])
        ) {
          processedLength++;
        }
        continue;
      } else if (result.isIncomplete) {
        // JSON不完整，等待更多数据
        break;
      } else {
        // 不是JSON格式，可能是其他数据，跳过第一个字符
        processedLength = 1;
        continue;
      }
    }

    // 情况2: 查找下一个"data:"标记
    const dataIndex = buffer.indexOf("data:", processedLength);
    if (dataIndex === -1) {
      // 没有找到"data:"，检查是否有不完整的"data:"前缀
      const remaining = buffer.substring(processedLength);
      if (isIncompleteDataPrefix(remaining)) {
        // 有不完整的"data:"前缀，保留等待更多数据
        break;
      } else {
        // 没有"data:"相关内容，可能是垃圾数据，全部丢弃
        processedLength = buffer.length;
        break;
      }
    }

    // 情况3: 处理找到的"data:"块
    const jsonStart = findJsonStart(buffer, dataIndex + 5);
    if (jsonStart === -1) {
      // "data:"后面没有JSON，可能被截断，保留等待更多数据
      if (
        dataIndex + 5 >= buffer.length ||
        isDataComplete(buffer.substring(dataIndex))
      ) {
        processedLength = dataIndex + 5;
        continue;
      } else {
        break;
      }
    }

    // 情况4: 提取JSON对象
    const result = extractCompleteJsonFromPosition(buffer, jsonStart);
    if (result.jsonStr) {
      const parsed = tryParseJson(result.jsonStr);
      if (parsed) {
        parsedData.push(parsed);
      }
      processedLength = jsonStart + result.jsonStr.length;
      // 跳过JSON后的空白字符
      while (
        processedLength < buffer.length &&
        /\s/.test(buffer[processedLength])
      ) {
        processedLength++;
      }
    } else if (result.isIncomplete) {
      // JSON不完整，保留等待更多数据
      break;
    } else {
      // JSON格式错误，跳过这个"data:"
      processedLength = dataIndex + 5;
    }
  }

  return {
    parsedData,
    remainingBuffer: buffer.substring(processedLength),
  };
}

/**
 * 尝试从字符串开始位置解析JSON对象（用于处理没有data:前缀的续传数据）
 * 返回解析结果，包含JSON字符串和是否完整的标识
 */
function tryParseJsonFromStart(data: string): {
  jsonStr: string | null;
  isIncomplete: boolean;
} {
  const trimmedData = data.trim();
  if (!trimmedData.startsWith("{")) {
    return { jsonStr: null, isIncomplete: false };
  }

  return extractCompleteJsonFromPosition(trimmedData, 0);
}

/**
 * 从指定位置开始提取完整的JSON对象字符串
 * 返回解析结果，包含JSON字符串和是否完整的标识
 */
function extractCompleteJsonFromPosition(
  data: string,
  startIndex: number,
): {
  jsonStr: string | null;
  isIncomplete: boolean;
} {
  const len = data.length;
  if (startIndex >= len || data[startIndex] !== "{") {
    return { jsonStr: null, isIncomplete: false };
  }

  // 动态规划状态定义
  const dp: Array<{
    inString: boolean;
    escapeNext: boolean;
    braceCount: number;
    isValid: boolean;
  }> = Array.from({ length: len - startIndex });

  // 初始状态
  dp[0] = {
    inString: false,
    escapeNext: false,
    braceCount: 1, // 第一个字符是 '{'
    isValid: true,
  };

  // 动态规划转移
  for (let i = 1; i < len - startIndex; i++) {
    const char = data[startIndex + i];
    const prev = dp[i - 1];

    // 如果前一个状态无效，当前状态也无效
    if (!prev.isValid) {
      dp[i] = { ...prev, isValid: false };
      continue;
    }

    const currentState = {
      inString: prev.inString,
      escapeNext: false,
      braceCount: prev.braceCount,
      isValid: true,
    };

    // 处理转义字符
    if (prev.escapeNext) {
      // 前一个字符是转义字符，当前字符被转义
      dp[i] = currentState;
      continue;
    }

    if (char === "\\") {
      currentState.escapeNext = true;
    } else if (char === '"') {
      // 引号切换字符串状态
      currentState.inString = !prev.inString;
    } else if (!prev.inString) {
      // 不在字符串内部，处理括号
      if (char === "{") {
        currentState.braceCount++;
      } else if (char === "}") {
        currentState.braceCount--;

        // 找到完整的JSON对象
        if (currentState.braceCount === 0) {
          return {
            jsonStr: data.substring(startIndex, startIndex + i + 1),
            isIncomplete: false,
          };
        }

        // 括号不匹配，状态无效
        if (currentState.braceCount < 0) {
          currentState.isValid = false;
        }
      }
    }

    dp[i] = currentState;
  }

  // 没有找到完整的JSON对象，可能是数据被截断
  const lastState = dp[dp.length - 1];
  const isIncomplete =
    lastState && lastState.isValid && lastState.braceCount > 0;

  return { jsonStr: null, isIncomplete };
}

/**
 * 重置解析器的内部缓存
 */
export function resetStreamingParser(): void {
  incompleteBuffer = "";
}
