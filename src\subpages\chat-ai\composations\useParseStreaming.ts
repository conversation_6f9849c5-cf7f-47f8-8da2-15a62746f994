import type { ChunkData } from "@/api/servers/chat/type";

// 全局缓存，用于存储未完成的数据
let incompleteBuffer = "";

// 移除不再需要的函数，简化代码

/**
 * 去除流式数据中的"data:"前缀，并将剩余部分解析为JSON对象数组。
 * 优化版本：基于正确的流式数据格式理解
 *
 * 流式数据格式规则：
 * 1. 每个数据块格式：data: {JSON对象}
 * 2. 如果数据块被截断，后续chunk只包含JSON剩余部分，不重复data:前缀
 * 3. 只有当前数据块完全传输完成后，才开始新的data:块
 *
 * 支持的截断情况：
 * - "data:" 前缀被截断
 * - JSON对象在任意位置被截断
 * - 多个完整data块之间的截断
 *
 * @param data - 流式数据chunk
 * @returns 解析后的JSON对象数组
 */
export function parseStreamingData(data: string): ChunkData[] {
  const parsedData: ChunkData[] = [];

  // 将新数据追加到缓存中
  incompleteBuffer += data;

  console.log(
    "当前缓存内容:",
    incompleteBuffer.substring(0, 200) +
      (incompleteBuffer.length > 200 ? "..." : ""),
  );

  // 处理缓存中的数据
  const processResult = processStreamData();
  parsedData.push(...processResult.parsedData);

  // 更新缓存
  incompleteBuffer = processResult.remainingBuffer;

  console.log("解析到的数据条数:", parsedData.length);
  console.log("剩余缓存长度:", incompleteBuffer.length);

  return parsedData;
}

// 移除不再需要的函数

/**
 * 安全地解析JSON字符串
 */
function tryParseJson(jsonStr: string): ChunkData | null {
  try {
    const parsed = JSON.parse(jsonStr);
    if (typeof parsed === "object" && parsed !== null) {
      return parsed as ChunkData;
    }
  } catch (error) {
    console.warn(
      `无法解析JSON字符串: ${jsonStr.substring(0, 100)}...。错误: ${
        (error as Error).message
      }`,
    );
  }
  return null;
}

/**
 * 处理流式数据，基于正确的数据格式理解
 *
 * 核心逻辑：
 * 1. 如果缓存不以"data:"开头，说明是JSON续传数据
 * 2. 如果缓存以"data:"开头，解析完整的data块
 * 3. 一个data块只有一个"data:"前缀，截断后的续传不会有前缀
 */
function processStreamData(): {
  parsedData: ChunkData[];
  remainingBuffer: string;
} {
  const parsedData: ChunkData[] = [];
  const buffer = incompleteBuffer;
  let processedLength = 0;

  while (processedLength < buffer.length) {
    const remainingBuffer = buffer.substring(processedLength);

    // 情况1: 缓存不以"data:"开头，说明是JSON续传数据
    if (!remainingBuffer.trim().startsWith("data:")) {
      const result = tryParseJsonFromStart(remainingBuffer.trim());
      if (result.jsonStr) {
        // 找到完整的JSON，解析它
        const parsed = tryParseJson(result.jsonStr);
        if (parsed) {
          parsedData.push(parsed);
        }
        // 计算实际处理的长度（包括前面的空白字符）
        const trimmedStart =
          remainingBuffer.length - remainingBuffer.trim().length;
        processedLength += trimmedStart + result.jsonStr.length;

        // 跳过JSON后的空白字符
        while (
          processedLength < buffer.length &&
          /\s/.test(buffer[processedLength])
        ) {
          processedLength++;
        }
        continue;
      } else if (result.isIncomplete) {
        // JSON不完整，等待更多数据
        break;
      } else {
        // 不是有效的JSON，可能是垃圾数据，跳过一个字符
        processedLength++;
        continue;
      }
    }

    // 情况2: 处理"data:"开头的数据块
    const dataMatch = remainingBuffer.match(/^\s*data:\s*/);
    if (!dataMatch) {
      // 不是"data:"开头，但前面的逻辑已经处理了，这里应该不会到达
      processedLength++;
      continue;
    }

    // 找到"data:"，定位JSON开始位置
    const dataPrefix = dataMatch[0];
    const jsonStart = processedLength + dataPrefix.length;

    if (jsonStart >= buffer.length) {
      // "data:"后面没有内容，等待更多数据
      break;
    }

    if (buffer[jsonStart] !== "{") {
      // "data:"后面不是JSON开始，可能还在等待JSON数据
      break;
    }

    // 尝试提取完整的JSON对象
    const result = extractCompleteJsonFromPosition(buffer, jsonStart);
    if (result.jsonStr) {
      // 找到完整的JSON
      const parsed = tryParseJson(result.jsonStr);
      if (parsed) {
        parsedData.push(parsed);
      }
      processedLength = jsonStart + result.jsonStr.length;

      // 跳过JSON后的空白字符
      while (
        processedLength < buffer.length &&
        /\s/.test(buffer[processedLength])
      ) {
        processedLength++;
      }
    } else if (result.isIncomplete) {
      // JSON不完整，等待更多数据
      break;
    } else {
      // JSON格式错误，跳过这个"data:"
      processedLength = jsonStart;
    }
  }

  return {
    parsedData,
    remainingBuffer: buffer.substring(processedLength),
  };
}

/**
 * 尝试从字符串开始位置解析JSON对象（用于处理没有data:前缀的续传数据）
 * 返回解析结果，包含JSON字符串和是否完整的标识
 */
function tryParseJsonFromStart(data: string): {
  jsonStr: string | null;
  isIncomplete: boolean;
} {
  const trimmedData = data.trim();
  if (!trimmedData.startsWith("{")) {
    return { jsonStr: null, isIncomplete: false };
  }

  return extractCompleteJsonFromPosition(trimmedData, 0);
}

/**
 * 从指定位置开始提取完整的JSON对象字符串
 * 返回解析结果，包含JSON字符串和是否完整的标识
 */
function extractCompleteJsonFromPosition(
  data: string,
  startIndex: number,
): {
  jsonStr: string | null;
  isIncomplete: boolean;
} {
  const len = data.length;
  if (startIndex >= len || data[startIndex] !== "{") {
    return { jsonStr: null, isIncomplete: false };
  }

  // 动态规划状态定义
  const dp: Array<{
    inString: boolean;
    escapeNext: boolean;
    braceCount: number;
    isValid: boolean;
  }> = Array.from({ length: len - startIndex });

  // 初始状态
  dp[0] = {
    inString: false,
    escapeNext: false,
    braceCount: 1, // 第一个字符是 '{'
    isValid: true,
  };

  // 动态规划转移
  for (let i = 1; i < len - startIndex; i++) {
    const char = data[startIndex + i];
    const prev = dp[i - 1];

    // 如果前一个状态无效，当前状态也无效
    if (!prev.isValid) {
      dp[i] = { ...prev, isValid: false };
      continue;
    }

    const currentState = {
      inString: prev.inString,
      escapeNext: false,
      braceCount: prev.braceCount,
      isValid: true,
    };

    // 处理转义字符
    if (prev.escapeNext) {
      // 前一个字符是转义字符，当前字符被转义
      dp[i] = currentState;
      continue;
    }

    if (char === "\\") {
      currentState.escapeNext = true;
    } else if (char === '"') {
      // 引号切换字符串状态
      currentState.inString = !prev.inString;
    } else if (!prev.inString) {
      // 不在字符串内部，处理括号
      if (char === "{") {
        currentState.braceCount++;
      } else if (char === "}") {
        currentState.braceCount--;

        // 找到完整的JSON对象
        if (currentState.braceCount === 0) {
          return {
            jsonStr: data.substring(startIndex, startIndex + i + 1),
            isIncomplete: false,
          };
        }

        // 括号不匹配，状态无效
        if (currentState.braceCount < 0) {
          currentState.isValid = false;
        }
      }
    }

    dp[i] = currentState;
  }

  // 没有找到完整的JSON对象，可能是数据被截断
  const lastState = dp[dp.length - 1];
  const isIncomplete =
    lastState && lastState.isValid && lastState.braceCount > 0;

  return { jsonStr: null, isIncomplete };
}

/**
 * 重置解析器的内部缓存
 */
export function resetStreamingParser(): void {
  incompleteBuffer = "";
}
