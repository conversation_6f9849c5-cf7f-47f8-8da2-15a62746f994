import { parseStreamingData, resetStreamingParser } from "./useParseStreaming";

// 模拟测试数据
const testData1 = { type: "message", content: "Hello" };
const testData2 = { type: "message", content: "World" };
const testData3 = { type: "end", content: "Done" };

describe("useParseStreaming 优化版本测试 - 基于正确的流式数据理解", () => {
  beforeEach(() => {
    resetStreamingParser();
  });

  test("情况1: data: 前缀被截断", () => {
    // 模拟 "data:" 被截断的情况
    const chunk1 = "d";
    const chunk2 = "ata: ";
    const chunk3 = JSON.stringify(testData1);

    const result1 = parseStreamingData(chunk1);
    expect(result1).toHaveLength(0);

    const result2 = parseStreamingData(chunk2);
    expect(result2).toHaveLength(0);

    const result3 = parseStreamingData(chunk3);
    expect(result3).toHaveLength(1);
    expect(result3[0]).toEqual(testData1);
  });

  test("情况2: JSON对象被截断，续传无data:前缀", () => {
    const jsonStr = JSON.stringify(testData1);
    // 第一个chunk: data: + JSON的前半部分
    const chunk1 = "data: " + jsonStr.substring(0, 10);
    // 第二个chunk: JSON的后半部分（无data:前缀）
    const chunk2 = jsonStr.substring(10);

    const result1 = parseStreamingData(chunk1);
    expect(result1).toHaveLength(0); // JSON不完整

    const result2 = parseStreamingData(chunk2);
    expect(result2).toHaveLength(1); // JSON完整了
    expect(result2[0]).toEqual(testData1);
  });

  test("情况3: 完整data块 + 截断的新data块", () => {
    const json1 = JSON.stringify(testData1);
    const json2 = JSON.stringify(testData2);

    // chunk1: 完整的第一个data块 + 第二个data块的开始部分
    const chunk1 = `data: ${json1}\ndata: ${json2.substring(0, 10)}`;
    // chunk2: 第二个data块的剩余部分（无data:前缀）
    const chunk2 = json2.substring(10);

    const result1 = parseStreamingData(chunk1);
    expect(result1).toHaveLength(1); // 只解析出第一个完整的
    expect(result1[0]).toEqual(testData1);

    const result2 = parseStreamingData(chunk2);
    expect(result2).toHaveLength(1); // 第二个data块完整了
    expect(result2[0]).toEqual(testData2);
  });

  test("情况4: 多次截断的JSON续传", () => {
    const json1 = JSON.stringify(testData1);

    // 将一个data块分成多个chunk
    const chunk1 = "data: " + json1.substring(0, 8); // data: + JSON开始
    const chunk2 = json1.substring(8, 16); // JSON中间部分1
    const chunk3 = json1.substring(16, 24); // JSON中间部分2
    const chunk4 = json1.substring(24); // JSON结尾

    const result1 = parseStreamingData(chunk1);
    expect(result1).toHaveLength(0);

    const result2 = parseStreamingData(chunk2);
    expect(result2).toHaveLength(0);

    const result3 = parseStreamingData(chunk3);
    expect(result3).toHaveLength(0);

    const result4 = parseStreamingData(chunk4);
    expect(result4).toHaveLength(1);
    expect(result4[0]).toEqual(testData1);
  });

  test("情况5: 复杂的混合截断场景", () => {
    const json1 = JSON.stringify(testData1);
    const json2 = JSON.stringify(testData2);
    const json3 = JSON.stringify(testData3);

    // 模拟非常复杂的截断情况
    const chunk1 = "da";
    const chunk2 = "ta: " + json1.substring(0, 8);
    const chunk3 = json1.substring(8) + "\ndat";
    const chunk4 = "a: " + json2 + "\ndata: " + json3.substring(0, 5);
    const chunk5 = json3.substring(5);

    const result1 = parseStreamingData(chunk1);
    expect(result1).toHaveLength(0);

    const result2 = parseStreamingData(chunk2);
    expect(result2).toHaveLength(0);

    const result3 = parseStreamingData(chunk3);
    expect(result3).toHaveLength(1);
    expect(result3[0]).toEqual(testData1);

    const result4 = parseStreamingData(chunk4);
    expect(result4).toHaveLength(1);
    expect(result4[0]).toEqual(testData2);

    const result5 = parseStreamingData(chunk5);
    expect(result5).toHaveLength(1);
    expect(result5[0]).toEqual(testData3);
  });

  test("边界情况: 空数据和无效数据", () => {
    const result1 = parseStreamingData("");
    expect(result1).toHaveLength(0);

    const result2 = parseStreamingData("   ");
    expect(result2).toHaveLength(0);

    const result3 = parseStreamingData("invalid data");
    expect(result3).toHaveLength(0);
  });

  test("边界情况: 不完整的data前缀", () => {
    const result1 = parseStreamingData("d");
    expect(result1).toHaveLength(0);

    const result2 = parseStreamingData("ata:");
    expect(result2).toHaveLength(0);

    const result3 = parseStreamingData(" " + JSON.stringify(testData1));
    expect(result3).toHaveLength(1);
    expect(result3[0]).toEqual(testData1);
  });
});
