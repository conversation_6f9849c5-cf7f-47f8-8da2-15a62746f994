import { parseStreamingData, resetStreamingParser } from "./useParseStreaming";

// 模拟测试数据
const testData1 = { type: "message", content: "Hello" };
const testData2 = { type: "message", content: "World" };
const testData3 = { type: "end", content: "Done" };

describe("useParseStreaming 简化版本测试", () => {
  beforeEach(() => {
    resetStreamingParser();
  });

  it("情况1: JSON对象被截断，续传无data:前缀", () => {
    const jsonStr = JSON.stringify(testData1);
    // 第一个chunk: data: + JSON的前半部分
    const chunk1 = `data: ${jsonStr.substring(0, 10)}`;
    // 第二个chunk: JSON的后半部分（无data:前缀）
    const chunk2 = jsonStr.substring(10);

    const result1 = parseStreamingData(chunk1);
    expect(result1).toHaveLength(0); // JSON不完整

    const result2 = parseStreamingData(chunk2);
    expect(result2).toHaveLength(1); // JSON完整了
    expect(result2[0]).toEqual(testData1);
  });

  it("情况3: 完整data块 + 截断的新data块", () => {
    const json1 = JSON.stringify(testData1);
    const json2 = JSON.stringify(testData2);

    // chunk1: 完整的第一个data块 + 第二个data块的开始部分
    const chunk1 = `data: ${json1}\ndata: ${json2.substring(0, 10)}`;
    // chunk2: 第二个data块的剩余部分（无data:前缀）
    const chunk2 = json2.substring(10);

    const result1 = parseStreamingData(chunk1);
    expect(result1).toHaveLength(1); // 只解析出第一个完整的
    expect(result1[0]).toEqual(testData1);

    const result2 = parseStreamingData(chunk2);
    expect(result2).toHaveLength(1); // 第二个data块完整了
    expect(result2[0]).toEqual(testData2);
  });

  it("情况4: 多次截断的JSON续传", () => {
    const json1 = JSON.stringify(testData1);

    // 将一个data块分成多个chunk
    const chunk1 = `data: ${json1.substring(0, 8)}`; // data: + JSON开始
    const chunk2 = json1.substring(8, 16); // JSON中间部分1
    const chunk3 = json1.substring(16, 24); // JSON中间部分2
    const chunk4 = json1.substring(24); // JSON结尾

    const result1 = parseStreamingData(chunk1);
    expect(result1).toHaveLength(0);

    const result2 = parseStreamingData(chunk2);
    expect(result2).toHaveLength(0);

    const result3 = parseStreamingData(chunk3);
    expect(result3).toHaveLength(0);

    const result4 = parseStreamingData(chunk4);
    expect(result4).toHaveLength(1);
    expect(result4[0]).toEqual(testData1);
  });

  it("情况5: 真实的复杂截断场景 - 第三次返回多个完整JSON", () => {
    const json1 = JSON.stringify(testData1);
    const json2 = JSON.stringify(testData2);
    const json3 = JSON.stringify(testData3);

    // 模拟真实的接口返回场景
    // 第一次：data: + 被截断的JSON开始
    const chunk1 = `data: ${json1.substring(0, 15)}`;

    // 第二次：JSON中间部分（无data:前缀）
    const chunk2 = json1.substring(15, 30);

    // 第三次：JSON结尾 + 多个完整的data块
    const chunk3 = `${json1.substring(30)}\ndata: ${json2}\ndata: ${json3}`;

    const result1 = parseStreamingData(chunk1);
    expect(result1).toHaveLength(0); // JSON不完整

    const result2 = parseStreamingData(chunk2);
    expect(result2).toHaveLength(0); // JSON还是不完整

    const result3 = parseStreamingData(chunk3);
    expect(result3).toHaveLength(3); // 应该解析出3个JSON
    expect(result3[0]).toEqual(testData1); // 第一个被截断的JSON完成了
    expect(result3[1]).toEqual(testData2); // 第二个完整的JSON
    expect(result3[2]).toEqual(testData3); // 第三个完整的JSON
  });

  it("情况6: 一次返回多个完整data块（无截断）", () => {
    const json1 = JSON.stringify(testData1);
    const json2 = JSON.stringify(testData2);
    const json3 = JSON.stringify(testData3);

    // 一次返回多个完整的data块
    const chunk1 = `data: ${json1}\ndata: ${json2}\ndata: ${json3}`;

    const result1 = parseStreamingData(chunk1);
    expect(result1).toHaveLength(3);
    expect(result1[0]).toEqual(testData1);
    expect(result1[1]).toEqual(testData2);
    expect(result1[2]).toEqual(testData3);
  });

  it("情况7: 只被截断两次的场景", () => {
    const json1 = JSON.stringify(testData1);
    const json2 = JSON.stringify(testData2);

    // 第一次：data: + JSON开始部分
    const chunk1 = `data: ${json1.substring(0, 20)}`;

    // 第二次：JSON结尾 + 完整的第二个data块
    const chunk2 = `${json1.substring(20)}\ndata: ${json2}`;

    const result1 = parseStreamingData(chunk1);
    expect(result1).toHaveLength(0);

    const result2 = parseStreamingData(chunk2);
    expect(result2).toHaveLength(2); // 应该解析出2个JSON
    expect(result2[0]).toEqual(testData1);
    expect(result2[1]).toEqual(testData2);
  });

  it("情况8: 场景五 - 简单的两次截断，第一次不解析", () => {
    const json1 = JSON.stringify(testData1);

    // 第一次：data: + JSON开始部分（不完整）
    const chunk1 = `data: ${json1.substring(0, 15)}`;

    // 第二次：JSON结尾部分（完整）
    const chunk2 = json1.substring(15);

    const result1 = parseStreamingData(chunk1);
    expect(result1).toHaveLength(0); // 第一次不应该解析出任何JSON

    const result2 = parseStreamingData(chunk2);
    expect(result2).toHaveLength(1); // 第二次应该解析出1个完整的JSON
    expect(result2[0]).toEqual(testData1);
  });

  it("边界情况: 空数据和无效数据", () => {
    const result1 = parseStreamingData("");
    expect(result1).toHaveLength(0);

    const result2 = parseStreamingData("   ");
    expect(result2).toHaveLength(0);

    const result3 = parseStreamingData("invalid data");
    expect(result3).toHaveLength(0);
  });

  it("边界情况: 不完整的data前缀", () => {
    const result1 = parseStreamingData("d");
    expect(result1).toHaveLength(0);

    const result2 = parseStreamingData("ata:");
    expect(result2).toHaveLength(0);

    const result3 = parseStreamingData(` ${JSON.stringify(testData1)}`);
    expect(result3).toHaveLength(1);
    expect(result3[0]).toEqual(testData1);
  });
});
