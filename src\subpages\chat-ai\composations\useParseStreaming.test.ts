import { parseStreamingData, resetStreamingParser } from "./useParseStreaming";

// 模拟测试数据
const testData1 = { type: "message", content: "Hello" };
const testData2 = { type: "message", content: "World" };
const testData3 = { type: "end", content: "Done" };

describe("useParseStreaming 优化版本测试", () => {
  beforeEach(() => {
    resetStreamingParser();
  });

  test("情况1: data: 前缀被截断为多个部分", () => {
    // 模拟 "data:" 被截断的情况
    const chunk1 = "d";
    const chunk2 = "ata: ";
    const chunk3 = JSON.stringify(testData1);

    const result1 = parseStreamingData(chunk1);
    expect(result1).toHaveLength(0);

    const result2 = parseStreamingData(chunk2);
    expect(result2).toHaveLength(0);

    const result3 = parseStreamingData(chunk3);
    expect(result3).toHaveLength(1);
    expect(result3[0]).toEqual(testData1);
  });

  test("情况2: JSON对象被截断为多个部分", () => {
    const jsonStr = JSON.stringify(testData1);
    const chunk1 = "data: " + jsonStr.substring(0, 10);
    const chunk2 = jsonStr.substring(10, 20);
    const chunk3 = jsonStr.substring(20);

    const result1 = parseStreamingData(chunk1);
    expect(result1).toHaveLength(0);

    const result2 = parseStreamingData(chunk2);
    expect(result2).toHaveLength(0);

    const result3 = parseStreamingData(chunk3);
    expect(result3).toHaveLength(1);
    expect(result3[0]).toEqual(testData1);
  });

  test("情况3: 多个data块混合截断", () => {
    const json1 = JSON.stringify(testData1);
    const json2 = JSON.stringify(testData2);
    
    // chunk1包含完整的第一个data块和第二个data块的一部分
    const chunk1 = `data: ${json1}\ndata: ${json2.substring(0, 10)}`;
    const chunk2 = json2.substring(10);

    const result1 = parseStreamingData(chunk1);
    expect(result1).toHaveLength(1);
    expect(result1[0]).toEqual(testData1);

    const result2 = parseStreamingData(chunk2);
    expect(result2).toHaveLength(1);
    expect(result2[0]).toEqual(testData2);
  });

  test("情况4: 跨chunk的JSON续传数据（无data:前缀）", () => {
    const json1 = JSON.stringify(testData1);
    const json2 = JSON.stringify(testData2);
    
    // 第一个chunk包含完整的data块
    const chunk1 = `data: ${json1.substring(0, 15)}`;
    // 第二个chunk是JSON的续传部分（没有data:前缀）
    const chunk2 = json1.substring(15);
    // 第三个chunk包含新的data块
    const chunk3 = `\ndata: ${json2}`;

    const result1 = parseStreamingData(chunk1);
    expect(result1).toHaveLength(0);

    const result2 = parseStreamingData(chunk2);
    expect(result2).toHaveLength(1);
    expect(result2[0]).toEqual(testData1);

    const result3 = parseStreamingData(chunk3);
    expect(result3).toHaveLength(1);
    expect(result3[0]).toEqual(testData2);
  });

  test("情况5: 复杂的混合截断场景", () => {
    const json1 = JSON.stringify(testData1);
    const json2 = JSON.stringify(testData2);
    const json3 = JSON.stringify(testData3);
    
    // 模拟非常复杂的截断情况
    const chunk1 = "da";
    const chunk2 = "ta: " + json1.substring(0, 8);
    const chunk3 = json1.substring(8) + "\ndat";
    const chunk4 = "a: " + json2 + "\ndata: " + json3.substring(0, 5);
    const chunk5 = json3.substring(5);

    const result1 = parseStreamingData(chunk1);
    expect(result1).toHaveLength(0);

    const result2 = parseStreamingData(chunk2);
    expect(result2).toHaveLength(0);

    const result3 = parseStreamingData(chunk3);
    expect(result3).toHaveLength(1);
    expect(result3[0]).toEqual(testData1);

    const result4 = parseStreamingData(chunk4);
    expect(result4).toHaveLength(1);
    expect(result4[0]).toEqual(testData2);

    const result5 = parseStreamingData(chunk5);
    expect(result5).toHaveLength(1);
    expect(result5[0]).toEqual(testData3);
  });

  test("边界情况: 空数据和无效数据", () => {
    const result1 = parseStreamingData("");
    expect(result1).toHaveLength(0);

    const result2 = parseStreamingData("   ");
    expect(result2).toHaveLength(0);

    const result3 = parseStreamingData("invalid data");
    expect(result3).toHaveLength(0);
  });

  test("边界情况: 不完整的data前缀", () => {
    const result1 = parseStreamingData("d");
    expect(result1).toHaveLength(0);

    const result2 = parseStreamingData("ata:");
    expect(result2).toHaveLength(0);

    const result3 = parseStreamingData(" " + JSON.stringify(testData1));
    expect(result3).toHaveLength(1);
    expect(result3[0]).toEqual(testData1);
  });
});
