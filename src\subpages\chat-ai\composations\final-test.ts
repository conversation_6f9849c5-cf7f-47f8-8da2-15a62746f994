import { parseStreamingData, resetStreamingParser } from "./useParseStreaming";

// 最终测试场景三
console.log("=== 最终测试场景三 ===");

function testScenario3() {
  resetStreamingParser();
  
  const json1 = '{"type":"start"}';
  const json2 = '{"type":"middle"}';
  const json3 = '{"type":"end"}';
  
  console.log("测试数据:");
  console.log("JSON1:", json1);
  console.log("JSON2:", json2);
  console.log("JSON3:", json3);
  
  // 第一次：data: + JSON开始部分
  const chunk1 = "data: " + json1.substring(0, 8); // "data: {"type""
  console.log("\nChunk1:", `"${chunk1}"`);
  
  const result1 = parseStreamingData(chunk1);
  console.log("Result1 长度:", result1.length);
  
  // 第二次：JSON中间部分
  const chunk2 = json1.substring(8, 12); // ":"sta"
  console.log("\nChunk2:", `"${chunk2}"`);
  
  const result2 = parseStreamingData(chunk2);
  console.log("Result2 长度:", result2.length);
  
  // 第三次：JSON结尾 + 多个完整data块
  const chunk3 = json1.substring(12) + `\ndata: ${json2}\ndata: ${json3}`; // "rt"} + data: {...} + data: {...}
  console.log("\nChunk3:", `"${chunk3}"`);
  
  const result3 = parseStreamingData(chunk3);
  console.log("Result3 长度:", result3.length);
  console.log("Result3 内容:", result3);
  
  console.log("\n期望结果: 0, 0, 3");
  console.log("实际结果:", result1.length, result2.length, result3.length);
  
  const success = result1.length === 0 && result2.length === 0 && result3.length === 3;
  console.log(success ? "✅ 测试通过" : "❌ 测试失败");
  
  return success;
}

// 测试其他场景
function testOtherScenarios() {
  console.log("\n=== 测试其他场景 ===");
  
  // 场景1: 简单的JSON截断
  resetStreamingParser();
  const json = '{"test": "value"}';
  const chunk1 = "data: " + json.substring(0, 10);
  const chunk2 = json.substring(10);
  
  const r1 = parseStreamingData(chunk1);
  const r2 = parseStreamingData(chunk2);
  
  console.log("场景1 - 简单截断:", r1.length === 0 && r2.length === 1 ? "✅" : "❌");
  
  // 场景2: 一次返回多个完整data块
  resetStreamingParser();
  const multiData = `data: {"a":1}\ndata: {"b":2}\ndata: {"c":3}`;
  const r3 = parseStreamingData(multiData);
  
  console.log("场景2 - 多个完整块:", r3.length === 3 ? "✅" : "❌");
  
  return true;
}

const test1 = testScenario3();
const test2 = testOtherScenarios();

console.log("\n=== 总结 ===");
console.log("场景三:", test1 ? "✅ 通过" : "❌ 失败");
console.log("其他场景:", test2 ? "✅ 通过" : "❌ 失败");

if (!test1) {
  console.log("\n场景三失败，可能的原因:");
  console.log("1. extractCompleteJsonFromPosition 函数有问题");
  console.log("2. 处理逻辑中的 processedLength 计算有误");
  console.log("3. 缓存管理有问题");
}
