import { parseStreamingData, resetStreamingParser } from "./useParseStreaming";

// 测试场景五：简单的两次截断，第一次不解析
console.log("=== 测试场景五 ===");

function testScenario5() {
  resetStreamingParser();

  const testData = { type: "message", content: "Hello World" };
  const jsonStr = JSON.stringify(testData);

  console.log("完整JSON:", jsonStr);
  console.log("JSON长度:", jsonStr.length);

  // 第一次：data: + JSON开始部分（不完整）
  const chunk1 = "data: " + jsonStr.substring(0, 15);
  console.log("\nChunk1:", `"${chunk1}"`);
  console.log("Chunk1长度:", chunk1.length);

  const result1 = parseStreamingData(chunk1);
  console.log("Result1 长度:", result1.length);
  console.log("Result1:", result1);

  // 第二次：JSON结尾部分（完整）
  const chunk2 = jsonStr.substring(15);
  console.log("\nChunk2:", `"${chunk2}"`);
  console.log("Chunk2长度:", chunk2.length);

  const result2 = parseStreamingData(chunk2);
  console.log("Result2 长度:", result2.length);
  console.log("Result2:", result2);

  // 验证结果
  const success =
    result1.length === 0 &&
    result2.length === 1 &&
    JSON.stringify(result2[0]) === jsonStr;

  if (success) {
    console.log("\n✅ 场景五测试通过！");
  } else {
    console.log("\n❌ 场景五测试失败！");
    console.log("期望: result1=[], result2=[完整JSON]");
    console.log("实际: result1=", result1, ", result2=", result2);
  }

  return success;
}

// 测试多种截断位置
console.log("\n=== 测试不同截断位置 ===");
const testData = { type: "test", content: "测试消息" };
const jsonStr = JSON.stringify(testData);

for (let cutPos = 10; cutPos < jsonStr.length - 5; cutPos += 5) {
  console.log(`\n--- 截断位置: ${cutPos} ---`);
  resetStreamingParser();

  const chunk1 = "data: " + jsonStr.substring(0, cutPos);
  const chunk2 = jsonStr.substring(cutPos);

  console.log(`Chunk1: "${chunk1}"`);
  console.log(`Chunk2: "${chunk2}"`);

  const result1 = parseStreamingData(chunk1);
  const result2 = parseStreamingData(chunk2);

  console.log(`Result1长度: ${result1.length}, Result2长度: ${result2.length}`);

  if (result1.length === 0 && result2.length === 1) {
    console.log("✅ 正确");
  } else {
    console.log("❌ 错误");
  }
}

testScenario5();
