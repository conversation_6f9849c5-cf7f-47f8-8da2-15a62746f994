// 综合测试所有场景

// 全局缓存，用于存储未完成的数据
let incompleteBuffer = "";

/**
 * 安全地解析JSON字符串
 */
function tryParseJson(jsonStr) {
  try {
    const parsed = JSON.parse(jsonStr);
    if (typeof parsed === "object" && parsed !== null) {
      return parsed;
    }
  } catch (error) {
    console.warn(
      `无法解析JSON字符串: ${jsonStr.substring(0, 100)}...。错误: ${error.message}`,
    );
  }
  return null;
}

/**
 * 尝试从字符串开始位置解析JSON对象
 */
function tryParseJsonFromStart(data) {
  const trimmedData = data.trim();
  if (!trimmedData.startsWith("{")) {
    return { jsonStr: null, isIncomplete: false };
  }

  return extractCompleteJsonFromPosition(trimmedData, 0);
}

/**
 * 从指定位置开始提取完整的JSON对象字符串
 */
function extractCompleteJsonFromPosition(data, startIndex) {
  const len = data.length;
  if (startIndex >= len || data[startIndex] !== "{") {
    return { jsonStr: null, isIncomplete: false };
  }

  let braceCount = 0;
  let inString = false;
  let escapeNext = false;

  for (let i = startIndex; i < len; i++) {
    const char = data[i];

    if (escapeNext) {
      escapeNext = false;
      continue;
    }

    if (char === "\\") {
      escapeNext = true;
      continue;
    }

    if (char === '"') {
      inString = !inString;
      continue;
    }

    if (!inString) {
      if (char === "{") {
        braceCount++;
      } else if (char === "}") {
        braceCount--;
        
        // 找到完整的JSON对象
        if (braceCount === 0) {
          return {
            jsonStr: data.substring(startIndex, i + 1),
            isIncomplete: false,
          };
        }
      }
    }
  }

  // 没有找到完整的JSON对象，可能是数据被截断
  const isIncomplete = braceCount > 0;
  return { jsonStr: null, isIncomplete };
}

/**
 * 处理流式数据
 */
function processStreamData() {
  const parsedData = [];
  const buffer = incompleteBuffer;
  let processedLength = 0;

  // 第一步：处理可能的JSON续传数据（无data:前缀）
  if (!buffer.trim().startsWith("data:")) {
    const result = tryParseJsonFromStart(buffer.trim());
    if (result.jsonStr) {
      // 找到完整的续传JSON
      const parsed = tryParseJson(result.jsonStr);
      if (parsed) {
        parsedData.push(parsed);
      }

      // 计算处理的长度（包括前面的空白字符）
      const trimmedStart = buffer.length - buffer.trim().length;
      processedLength = trimmedStart + result.jsonStr.length;

      // 跳过JSON后的空白字符和换行符
      while (
        processedLength < buffer.length &&
        /\s/.test(buffer[processedLength])
      ) {
        processedLength++;
      }
    } else if (result.isIncomplete) {
      // JSON不完整，等待更多数据
      return {
        parsedData,
        remainingBuffer: buffer,
      };
    } else {
      // 不是有效的JSON，跳过到第一个data:或结束
      const firstDataIndex = buffer.indexOf("data:");
      if (firstDataIndex !== -1) {
        processedLength = firstDataIndex;
      } else {
        // 没有data:，全部丢弃
        return {
          parsedData,
          remainingBuffer: "",
        };
      }
    }
  }

  // 第二步：循环处理所有完整的data:块
  while (processedLength < buffer.length) {
    const remainingBuffer = buffer.substring(processedLength);

    // 查找下一个data:
    const dataMatch = remainingBuffer.match(/^\s*data:\s*/);
    if (!dataMatch) {
      // 没有找到data:，结束处理
      break;
    }

    // 找到data:，定位JSON开始位置
    const dataPrefix = dataMatch[0];
    const jsonStart = processedLength + dataPrefix.length;

    if (jsonStart >= buffer.length) {
      // data:后面没有内容，等待更多数据
      break;
    }

    if (buffer[jsonStart] !== "{") {
      // data:后面不是JSON开始，可能还在等待JSON数据
      break;
    }

    // 尝试提取完整的JSON对象
    const result = extractCompleteJsonFromPosition(buffer, jsonStart);
    
    if (result.jsonStr) {
      // 找到完整的JSON
      const parsed = tryParseJson(result.jsonStr);
      if (parsed) {
        parsedData.push(parsed);
      }
      processedLength = jsonStart + result.jsonStr.length;

      // 跳过JSON后的空白字符和换行符
      while (
        processedLength < buffer.length &&
        /\s/.test(buffer[processedLength])
      ) {
        processedLength++;
      }
    } else if (result.isIncomplete) {
      // JSON不完整，等待更多数据
      break;
    } else {
      // JSON格式错误，跳过这个data:
      processedLength = jsonStart;
    }
  }

  return {
    parsedData,
    remainingBuffer: buffer.substring(processedLength),
  };
}

/**
 * 主要的解析函数
 */
function parseStreamingData(data) {
  const parsedData = [];

  // 将新数据追加到缓存中
  incompleteBuffer += data;

  // 处理缓存中的数据
  const processResult = processStreamData();
  parsedData.push(...processResult.parsedData);

  // 更新缓存
  incompleteBuffer = processResult.remainingBuffer;

  return parsedData;
}

/**
 * 重置解析器的内部缓存
 */
function resetStreamingParser() {
  incompleteBuffer = "";
}

// 测试所有场景
console.log("=== 综合测试所有场景 ===");

function runAllTests() {
  let passedTests = 0;
  let totalTests = 0;

  // 场景1: 简单的JSON截断
  console.log("\n--- 场景1: 简单的JSON截断 ---");
  resetStreamingParser();
  totalTests++;
  
  const json = '{"test": "value"}';
  const chunk1 = "data: " + json.substring(0, 10);
  const chunk2 = json.substring(10);
  
  const r1 = parseStreamingData(chunk1);
  const r2 = parseStreamingData(chunk2);
  
  const test1Pass = r1.length === 0 && r2.length === 1;
  console.log("结果:", r1.length, r2.length, test1Pass ? "✅" : "❌");
  if (test1Pass) passedTests++;

  // 场景2: 一次返回多个完整data块
  console.log("\n--- 场景2: 一次返回多个完整data块 ---");
  resetStreamingParser();
  totalTests++;
  
  const multiData = `data: {"a":1}\ndata: {"b":2}\ndata: {"c":3}`;
  const r3 = parseStreamingData(multiData);
  
  const test2Pass = r3.length === 3;
  console.log("结果:", r3.length, test2Pass ? "✅" : "❌");
  if (test2Pass) passedTests++;

  // 场景3: 复杂的三次截断
  console.log("\n--- 场景3: 复杂的三次截断 ---");
  resetStreamingParser();
  totalTests++;
  
  const json1 = '{"type":"start","content":"开始消息"}';
  const json2 = '{"type":"middle","content":"中间消息"}';
  const json3 = '{"type":"end","content":"结束消息"}';
  
  const chunk3_1 = "data: " + json1.substring(0, 20);
  const chunk3_2 = json1.substring(20, 30);
  const chunk3_3 = json1.substring(30) + `\ndata: ${json2}\ndata: ${json3}`;
  
  const r3_1 = parseStreamingData(chunk3_1);
  const r3_2 = parseStreamingData(chunk3_2);
  const r3_3 = parseStreamingData(chunk3_3);
  
  const test3Pass = r3_1.length === 0 && r3_2.length === 0 && r3_3.length === 3;
  console.log("结果:", r3_1.length, r3_2.length, r3_3.length, test3Pass ? "✅" : "❌");
  if (test3Pass) passedTests++;

  // 场景4: 场景五 - 简单两次截断
  console.log("\n--- 场景4: 场景五 - 简单两次截断 ---");
  resetStreamingParser();
  totalTests++;
  
  const simpleJson = '{"simple": "message"}';
  const chunk4_1 = "data: " + simpleJson.substring(0, 12);
  const chunk4_2 = simpleJson.substring(12);
  
  const r4_1 = parseStreamingData(chunk4_1);
  const r4_2 = parseStreamingData(chunk4_2);
  
  const test4Pass = r4_1.length === 0 && r4_2.length === 1;
  console.log("结果:", r4_1.length, r4_2.length, test4Pass ? "✅" : "❌");
  if (test4Pass) passedTests++;

  // 场景5: 复杂JSON内容
  console.log("\n--- 场景5: 复杂JSON内容 ---");
  resetStreamingParser();
  totalTests++;
  
  const complexJson = '{"nested": {"key": "value with \\"quotes\\"", "array": [1, 2, 3]}}';
  const chunk5_1 = "data: " + complexJson.substring(0, 25);
  const chunk5_2 = complexJson.substring(25);
  
  const r5_1 = parseStreamingData(chunk5_1);
  const r5_2 = parseStreamingData(chunk5_2);
  
  const test5Pass = r5_1.length === 0 && r5_2.length === 1;
  console.log("结果:", r5_1.length, r5_2.length, test5Pass ? "✅" : "❌");
  if (test5Pass) passedTests++;

  console.log("\n=== 测试总结 ===");
  console.log(`通过测试: ${passedTests}/${totalTests}`);
  console.log(`成功率: ${(passedTests/totalTests*100).toFixed(1)}%`);
  
  return passedTests === totalTests;
}

runAllTests();
