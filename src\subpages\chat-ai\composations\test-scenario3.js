// JavaScript 版本的测试，用于调试场景三

// 全局缓存，用于存储未完成的数据
let incompleteBuffer = "";

/**
 * 安全地解析JSON字符串
 */
function tryParseJson(jsonStr) {
  try {
    const parsed = JSON.parse(jsonStr);
    if (typeof parsed === "object" && parsed !== null) {
      return parsed;
    }
  } catch (error) {
    console.warn(
      `无法解析JSON字符串: ${jsonStr.substring(0, 100)}...。错误: ${error.message}`,
    );
  }
  return null;
}

/**
 * 尝试从字符串开始位置解析JSON对象
 */
function tryParseJsonFromStart(data) {
  const trimmedData = data.trim();
  if (!trimmedData.startsWith("{")) {
    return { jsonStr: null, isIncomplete: false };
  }

  return extractCompleteJsonFromPosition(trimmedData, 0);
}

/**
 * 从指定位置开始提取完整的JSON对象字符串
 */
function extractCompleteJsonFromPosition(data, startIndex) {
  const len = data.length;
  if (startIndex >= len || data[startIndex] !== "{") {
    return { jsonStr: null, isIncomplete: false };
  }

  let braceCount = 0;
  let inString = false;
  let escapeNext = false;

  for (let i = startIndex; i < len; i++) {
    const char = data[i];

    if (escapeNext) {
      escapeNext = false;
      continue;
    }

    if (char === "\\") {
      escapeNext = true;
      continue;
    }

    if (char === '"') {
      inString = !inString;
      continue;
    }

    if (!inString) {
      if (char === "{") {
        braceCount++;
      } else if (char === "}") {
        braceCount--;
        
        // 找到完整的JSON对象
        if (braceCount === 0) {
          return {
            jsonStr: data.substring(startIndex, i + 1),
            isIncomplete: false,
          };
        }
      }
    }
  }

  // 没有找到完整的JSON对象，可能是数据被截断
  const isIncomplete = braceCount > 0;
  return { jsonStr: null, isIncomplete };
}

/**
 * 处理流式数据
 */
function processStreamData() {
  const parsedData = [];
  const buffer = incompleteBuffer;
  let processedLength = 0;

  // 第一步：处理可能的JSON续传数据（无data:前缀）
  if (!buffer.trim().startsWith("data:")) {
    const result = tryParseJsonFromStart(buffer.trim());
    if (result.jsonStr) {
      // 找到完整的续传JSON
      const parsed = tryParseJson(result.jsonStr);
      if (parsed) {
        parsedData.push(parsed);
      }

      // 计算处理的长度（包括前面的空白字符）
      const trimmedStart = buffer.length - buffer.trim().length;
      processedLength = trimmedStart + result.jsonStr.length;

      // 跳过JSON后的空白字符和换行符
      while (
        processedLength < buffer.length &&
        /\s/.test(buffer[processedLength])
      ) {
        processedLength++;
      }
    } else if (result.isIncomplete) {
      // JSON不完整，等待更多数据
      return {
        parsedData,
        remainingBuffer: buffer,
      };
    } else {
      // 不是有效的JSON，跳过到第一个data:或结束
      const firstDataIndex = buffer.indexOf("data:");
      if (firstDataIndex !== -1) {
        processedLength = firstDataIndex;
      } else {
        // 没有data:，全部丢弃
        return {
          parsedData,
          remainingBuffer: "",
        };
      }
    }
  }

  // 第二步：循环处理所有完整的data:块
  while (processedLength < buffer.length) {
    const remainingBuffer = buffer.substring(processedLength);

    // 查找下一个data:
    const dataMatch = remainingBuffer.match(/^\s*data:\s*/);
    if (!dataMatch) {
      // 没有找到data:，结束处理
      break;
    }

    // 找到data:，定位JSON开始位置
    const dataPrefix = dataMatch[0];
    const jsonStart = processedLength + dataPrefix.length;

    if (jsonStart >= buffer.length) {
      // data:后面没有内容，等待更多数据
      break;
    }

    if (buffer[jsonStart] !== "{") {
      // data:后面不是JSON开始，可能还在等待JSON数据
      break;
    }

    // 尝试提取完整的JSON对象
    const result = extractCompleteJsonFromPosition(buffer, jsonStart);
    
    if (result.jsonStr) {
      // 找到完整的JSON
      const parsed = tryParseJson(result.jsonStr);
      if (parsed) {
        parsedData.push(parsed);
      }
      processedLength = jsonStart + result.jsonStr.length;

      // 跳过JSON后的空白字符和换行符
      while (
        processedLength < buffer.length &&
        /\s/.test(buffer[processedLength])
      ) {
        processedLength++;
      }
    } else if (result.isIncomplete) {
      // JSON不完整，等待更多数据
      break;
    } else {
      // JSON格式错误，跳过这个data:
      processedLength = jsonStart;
    }
  }

  return {
    parsedData,
    remainingBuffer: buffer.substring(processedLength),
  };
}

/**
 * 主要的解析函数
 */
function parseStreamingData(data) {
  const parsedData = [];

  // 将新数据追加到缓存中
  incompleteBuffer += data;

  console.log("当前缓存内容:", incompleteBuffer.substring(0, 200));

  // 处理缓存中的数据
  const processResult = processStreamData();
  parsedData.push(...processResult.parsedData);

  // 更新缓存
  incompleteBuffer = processResult.remainingBuffer;

  console.log("解析到的数据条数:", parsedData.length);
  console.log("剩余缓存长度:", incompleteBuffer.length);

  return parsedData;
}

/**
 * 重置解析器的内部缓存
 */
function resetStreamingParser() {
  incompleteBuffer = "";
}

// 测试场景三
console.log("=== 测试场景三 ===");

function testScenario3() {
  resetStreamingParser();
  
  const json1 = '{"type":"start"}';
  const json2 = '{"type":"middle"}';
  const json3 = '{"type":"end"}';
  
  console.log("测试数据:");
  console.log("JSON1:", json1);
  console.log("JSON2:", json2);
  console.log("JSON3:", json3);
  
  // 第一次：data: + JSON开始部分
  const chunk1 = "data: " + json1.substring(0, 8); // "data: {"type""
  console.log("\nChunk1:", `"${chunk1}"`);
  
  const result1 = parseStreamingData(chunk1);
  console.log("Result1 长度:", result1.length);
  
  // 第二次：JSON中间部分
  const chunk2 = json1.substring(8, 12); // ":"sta"
  console.log("\nChunk2:", `"${chunk2}"`);
  
  const result2 = parseStreamingData(chunk2);
  console.log("Result2 长度:", result2.length);
  
  // 第三次：JSON结尾 + 多个完整data块
  const chunk3 = json1.substring(12) + `\ndata: ${json2}\ndata: ${json3}`; // "rt"} + data: {...} + data: {...}
  console.log("\nChunk3:", `"${chunk3}"`);
  
  const result3 = parseStreamingData(chunk3);
  console.log("Result3 长度:", result3.length);
  console.log("Result3 内容:", result3);
  
  console.log("\n期望结果: 0, 0, 3");
  console.log("实际结果:", result1.length, result2.length, result3.length);
  
  const success = result1.length === 0 && result2.length === 0 && result3.length === 3;
  console.log(success ? "✅ 测试通过" : "❌ 测试失败");
  
  return success;
}

testScenario3();
