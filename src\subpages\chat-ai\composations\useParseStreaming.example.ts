import { parseStreamingData, resetStreamingParser } from "./useParseStreaming";

/**
 * 使用示例：基于正确的流式数据格式理解
 *
 * 核心理解：
 * - 每个数据块格式：data: {JSON对象}
 * - 如果被截断，后续chunk只包含JSON剩余部分，不重复data:前缀
 * - 只有当前数据块完全传输完成后，才开始新的data:块
 */

// 示例1: data:前缀被截断
console.log("=== 示例1: data:前缀被截断 ===");
resetStreamingParser();

const chunks1 = [
  "d", // data:被截断
  "ata: ", // data:继续
  '{"type": "message", "content": "Hello World"}', // JSON部分
];

chunks1.forEach((chunk, index) => {
  console.log(`处理chunk ${index + 1}: "${chunk}"`);
  const result = parseStreamingData(chunk);
  console.log(`解析结果:`, result);
});

// 示例2: JSON对象被截断，续传无data:前缀
console.log("\n=== 示例2: JSON对象被截断 ===");
resetStreamingParser();

const fullJson = '{"type": "message", "content": "This is a long message"}';
const chunks2 = [
  "data: " + fullJson.substring(0, 20), // data: + JSON前半部分
  fullJson.substring(20), // JSON后半部分（无data:前缀）
];

chunks2.forEach((chunk, index) => {
  console.log(`处理chunk ${index + 1}: "${chunk}"`);
  const result = parseStreamingData(chunk);
  console.log(`解析结果:`, result);
});

// 示例3: 处理多个data块混合截断
console.log("\n=== 示例3: 多个data块混合截断 ===");
resetStreamingParser();

const json1 = '{"type": "start", "content": "开始"}';
const json2 = '{"type": "middle", "content": "中间"}';
const json3 = '{"type": "end", "content": "结束"}';

const chunks3 = [
  `data: ${json1}\ndata: ${json2.substring(0, 15)}`, // 第一个完整，第二个截断
  json2.substring(15) + `\ndat`, // 第二个完成，第三个data:被截断
  `a: ${json3}`, // 第三个完成
];

chunks3.forEach((chunk, index) => {
  console.log(`处理chunk ${index + 1}: "${chunk}"`);
  const result = parseStreamingData(chunk);
  console.log(`解析结果:`, result);
});

// 示例4: 处理跨chunk的JSON续传（无data:前缀）
console.log("\n=== 示例4: 跨chunk的JSON续传 ===");
resetStreamingParser();

const longJson =
  '{"type": "message", "content": "这是一个很长的消息，可能会被分割到多个chunk中传输"}';
const chunks4 = [
  "data: " + longJson.substring(0, 30), // data:开头，但JSON不完整
  longJson.substring(30, 60), // 纯JSON续传，无data:前缀
  longJson.substring(60), // JSON结尾
];

chunks4.forEach((chunk, index) => {
  console.log(`处理chunk ${index + 1}: "${chunk}"`);
  const result = parseStreamingData(chunk);
  console.log(`解析结果:`, result);
});

// 示例5: 复杂的实际场景模拟
console.log("\n=== 示例5: 复杂实际场景 ===");
resetStreamingParser();

// 模拟实际的流式响应，包含多种截断情况
const realWorldChunks = [
  "da", // data:被截断
  "ta: ", // data:继续
  '{"type": "start", "id": 1}\ndata: {"ty', // 第一个JSON完整，第二个开始但被截断
  'pe": "chunk", "data": "Hello"}\ndat', // 第二个JSON完成，第三个data:被截断
  'a: {"type": "chunk", "data": " Wor', // 第三个开始但JSON被截断
  'ld"}\ndata: {"type": "end"}', // 第三个完成，第四个完整
];

realWorldChunks.forEach((chunk, index) => {
  console.log(`\n处理实际场景chunk ${index + 1}: "${chunk}"`);
  const result = parseStreamingData(chunk);
  console.log(`解析结果:`, result);
});

console.log("\n=== 所有示例完成 ===");
