import { parseStreamingData, resetStreamingParser } from "./useParseStreaming";

/**
 * 使用示例：基于正确的流式数据格式理解
 *
 * 核心理解：
 * - 每个数据块格式：data: {JSON对象}
 * - 如果被截断，后续chunk只包含JSON剩余部分，不重复data:前缀
 * - 只有当前数据块完全传输完成后，才开始新的data:块
 */

// 示例1: data:前缀被截断
console.log("=== 示例1: data:前缀被截断 ===");
resetStreamingParser();

const chunks1 = [
  "d", // data:被截断
  "ata: ", // data:继续
  '{"type": "message", "content": "Hello World"}', // JSON部分
];

chunks1.forEach((chunk, index) => {
  console.log(`处理chunk ${index + 1}: "${chunk}"`);
  const result = parseStreamingData(chunk);
  console.log(`解析结果:`, result);
});

// 示例2: JSON对象被截断，续传无data:前缀
console.log("\n=== 示例2: JSON对象被截断 ===");
resetStreamingParser();

const fullJson = '{"type": "message", "content": "This is a long message"}';
const chunks2 = [
  "data: " + fullJson.substring(0, 20), // data: + JSON前半部分
  fullJson.substring(20), // JSON后半部分（无data:前缀）
];

chunks2.forEach((chunk, index) => {
  console.log(`处理chunk ${index + 1}: "${chunk}"`);
  const result = parseStreamingData(chunk);
  console.log(`解析结果:`, result);
});

// 示例3: 真实的复杂截断场景 - 第三次返回多个完整JSON
console.log("\n=== 示例3: 真实复杂截断场景 ===");
resetStreamingParser();

const json1 = '{"type": "start", "content": "开始消息"}';
const json2 = '{"type": "middle", "content": "中间消息"}';
const json3 = '{"type": "end", "content": "结束消息"}';

const chunks3 = [
  // 第一次：data: + 被截断的JSON开始
  "data: " + json1.substring(0, 20),
  // 第二次：JSON中间部分（无data:前缀）
  json1.substring(20, 35),
  // 第三次：JSON结尾 + 多个完整的data块
  json1.substring(35) + `\ndata: ${json2}\ndata: ${json3}`,
];

chunks3.forEach((chunk, index) => {
  console.log(`处理chunk ${index + 1}: "${chunk}"`);
  const result = parseStreamingData(chunk);
  console.log(`解析结果数量: ${result.length}`);
  result.forEach((item, i) => console.log(`  JSON ${i + 1}:`, item));
});

// 示例4: 一次返回多个完整data块（无截断）
console.log("\n=== 示例4: 一次返回多个完整data块 ===");
resetStreamingParser();

const chunks4 = [`data: ${json1}\ndata: ${json2}\ndata: ${json3}`];

chunks4.forEach((chunk, index) => {
  console.log(`处理chunk ${index + 1}: "${chunk}"`);
  const result = parseStreamingData(chunk);
  console.log(`解析结果数量: ${result.length}`);
  result.forEach((item, i) => console.log(`  JSON ${i + 1}:`, item));
});

// 示例5: 只被截断两次的场景
console.log("\n=== 示例5: 只被截断两次 ===");
resetStreamingParser();

const chunks5 = [
  // 第一次：data: + JSON开始部分
  "data: " + json1.substring(0, 25),
  // 第二次：JSON结尾 + 完整的第二个data块
  json1.substring(25) + `\ndata: ${json2}`,
];

chunks5.forEach((chunk, index) => {
  console.log(`处理chunk ${index + 1}: "${chunk}"`);
  const result = parseStreamingData(chunk);
  console.log(`解析结果数量: ${result.length}`);
  result.forEach((item, i) => console.log(`  JSON ${i + 1}:`, item));
});

// 示例6: 极端复杂场景
console.log("\n=== 示例6: 极端复杂场景 ===");
resetStreamingParser();

const longJson =
  '{"type": "message", "content": "这是一个很长的消息内容，用来测试复杂的截断情况"}';
const chunks6 = [
  // 第一次：data:前缀被截断
  "dat",
  // 第二次：data:完成 + JSON开始
  "a: " + longJson.substring(0, 30),
  // 第三次：JSON中间部分
  longJson.substring(30, 60),
  // 第四次：JSON结尾 + 新的完整data块 + 另一个data块开始
  longJson.substring(60) + `\ndata: ${json2}\ndata: ${json3.substring(0, 20)}`,
  // 第五次：最后一个JSON的结尾
  json3.substring(20),
];

chunks6.forEach((chunk, index) => {
  console.log(`处理chunk ${index + 1}: "${chunk}"`);
  const result = parseStreamingData(chunk);
  console.log(`解析结果数量: ${result.length}`);
  result.forEach((item, i) => console.log(`  JSON ${i + 1}:`, item));
});

// 示例7: 场景五 - 简单的两次截断，第一次不解析
console.log("\n=== 示例7: 场景五 - 简单两次截断 ===");
resetStreamingParser();

const simpleJson = '{"type": "simple", "content": "简单消息"}';
const chunks7 = [
  // 第一次：data: + JSON开始部分（不完整）
  "data: " + simpleJson.substring(0, 20),
  // 第二次：JSON结尾部分（完整）
  simpleJson.substring(20),
];

chunks7.forEach((chunk, index) => {
  console.log(`处理chunk ${index + 1}: "${chunk}"`);
  const result = parseStreamingData(chunk);
  console.log(`解析结果数量: ${result.length}`);
  if (result.length > 0) {
    result.forEach((item, i) => console.log(`  JSON ${i + 1}:`, item));
  } else {
    console.log("  无解析结果（等待更多数据）");
  }
});

console.log("\n=== 所有示例完成 ===");
