// 测试真实的字节级截断情况

/**
 * 模拟从字节流中解码字符串，可能产生替换字符
 */
function simulateByteDecoding(originalStr, truncateBytePos) {
  const bytes = new TextEncoder().encode(originalStr);
  const truncatedBytes = bytes.slice(0, truncateBytePos);
  
  // 使用非严格模式解码，会产生替换字符
  const decoded = new TextDecoder("utf-8", { fatal: false }).decode(truncatedBytes);
  
  return {
    originalBytes: bytes,
    truncatedBytes: truncatedBytes,
    decoded: decoded,
    hasReplacementChar: decoded.includes('\uFFFD')
  };
}

/**
 * 检测字符串是否在多字节字符边界被截断
 */
function detectTruncatedCharacter(str) {
  if (!str) return -1;
  
  // 检查字符串末尾是否有替换字符（�），这通常表示字符被截断
  if (str.includes('\uFFFD')) {
    console.log("检测到替换字符，可能存在字符截断");
    
    // 从末尾开始查找安全位置（不包含替换字符的位置）
    for (let i = str.length - 1; i >= 0; i--) {
      if (str[i] === '\uFFFD') {
        // 找到第一个替换字符，返回其前面的位置
        console.log("检测到字符截断，安全位置:", i, "原长度:", str.length);
        return i;
      }
    }
  }
  
  return -1;
}

// 测试真实的字节级截断
function testRealTruncation() {
  console.log("=== 测试真实的字节级截断 ===");
  
  const testData = 'data: {"content": "这是一条包含中文的消息"}';
  console.log("原始数据:", testData);
  console.log("原始数据字节长度:", new TextEncoder().encode(testData).length);
  
  const bytes = new TextEncoder().encode(testData);
  
  // 测试在不同字节位置截断
  const testPositions = [
    bytes.length - 1,  // 截断最后1个字节
    bytes.length - 2,  // 截断最后2个字节
    bytes.length - 3,  // 截断最后3个字节
    bytes.length - 4,  // 截断最后4个字节
    bytes.length - 5,  // 截断最后5个字节
  ];
  
  for (const pos of testPositions) {
    console.log(`\n--- 在字节位置 ${pos} 截断 ---`);
    
    const result = simulateByteDecoding(testData, pos);
    
    console.log("截断后字符串:", `"${result.decoded}"`);
    console.log("包含替换字符:", result.hasReplacementChar);
    
    if (result.hasReplacementChar) {
      const detectResult = detectTruncatedCharacter(result.decoded);
      if (detectResult > 0) {
        console.log("✅ 检测到截断，安全位置:", detectResult);
        const safePart = result.decoded.substring(0, detectResult);
        const unsafePart = result.decoded.substring(detectResult);
        console.log("安全部分:", `"${safePart}"`);
        console.log("不安全部分:", `"${unsafePart}"`);
      } else {
        console.log("❌ 未能检测到截断");
      }
    } else {
      console.log("✅ 无替换字符，字符边界完整");
    }
  }
}

// 测试实际的流式数据处理场景
function testStreamingScenario() {
  console.log("\n=== 测试流式数据处理场景 ===");
  
  const json1 = '{"type": "start", "content": "开始消息"}';
  const json2 = '{"type": "middle", "content": "中间消息"}';
  const json3 = '{"type": "end", "content": "结束消息"}';
  
  // 模拟场景三，但在字节级别被截断
  const fullData = `data: ${json1}\ndata: ${json2}\ndata: ${json3}`;
  console.log("完整数据:", fullData);
  
  const bytes = new TextEncoder().encode(fullData);
  console.log("总字节数:", bytes.length);
  
  // 模拟在中文字符中间被截断
  const truncatePos = Math.floor(bytes.length * 0.3); // 在30%位置截断
  
  const chunk1Result = simulateByteDecoding(fullData, truncatePos);
  const chunk2Data = fullData.substring(chunk1Result.decoded.length);
  
  console.log("\nChunk1 (可能被截断):", `"${chunk1Result.decoded}"`);
  console.log("Chunk1 包含替换字符:", chunk1Result.hasReplacementChar);
  
  if (chunk1Result.hasReplacementChar) {
    const detectResult = detectTruncatedCharacter(chunk1Result.decoded);
    if (detectResult > 0) {
      console.log("✅ 检测到截断，需要分割处理");
      const safePart = chunk1Result.decoded.substring(0, detectResult);
      const unsafePart = chunk1Result.decoded.substring(detectResult);
      
      console.log("安全处理部分:", `"${safePart}"`);
      console.log("延迟处理部分:", `"${unsafePart}"`);
      console.log("Chunk2 数据:", `"${chunk2Data}"`);
      console.log("合并后的延迟部分:", `"${unsafePart + chunk2Data}"`);
    }
  }
}

testRealTruncation();
testStreamingScenario();
