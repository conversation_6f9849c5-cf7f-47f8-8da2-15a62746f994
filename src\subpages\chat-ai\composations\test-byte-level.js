// 测试字节级处理逻辑

// 全局缓存，用于存储未完成的数据
let incompleteBuffer = "";
// 字节级缓存，用于处理字符截断
let incompleteBytes = new Uint8Array(0);

/**
 * 从字节数组安全解码字符串
 */
function safeDecodeBytes(bytes) {
  // 尝试解码整个字节数组
  try {
    const decoded = new TextDecoder("utf-8", { fatal: true }).decode(bytes);
    return { decoded, remainingBytes: new Uint8Array(0) };
  } catch {
    // 解码失败，从末尾开始尝试找到安全的解码位置
    for (let i = bytes.length - 1; i >= Math.max(0, bytes.length - 4); i--) {
      try {
        const safeBytes = bytes.slice(0, i);
        const decoded = new TextDecoder("utf-8", { fatal: true }).decode(safeBytes);
        const remainingBytes = bytes.slice(i);
        console.log("字节级安全解码，安全长度:", i, "剩余字节:", remainingBytes.length);
        return { decoded, remainingBytes };
      } catch {
        continue;
      }
    }
    
    // 如果都无法解码，返回空字符串和原始字节
    console.warn("无法安全解码字节数组，长度:", bytes.length);
    return { decoded: "", remainingBytes: bytes };
  }
}

/**
 * 安全地解析JSON字符串
 */
function tryParseJson(jsonStr) {
  try {
    const parsed = JSON.parse(jsonStr);
    if (typeof parsed === "object" && parsed !== null) {
      return parsed;
    }
  } catch (error) {
    console.warn(`无法解析JSON字符串: ${jsonStr.substring(0, 100)}...。错误: ${error.message}`);
  }
  return null;
}

/**
 * 尝试从字符串开始位置解析JSON对象
 */
function tryParseJsonFromStart(data) {
  const trimmedData = data.trim();
  if (!trimmedData.startsWith("{")) {
    return { jsonStr: null, isIncomplete: false };
  }

  return extractCompleteJsonFromPosition(trimmedData, 0);
}

/**
 * 从指定位置开始提取完整的JSON对象字符串
 */
function extractCompleteJsonFromPosition(data, startIndex) {
  const len = data.length;
  if (startIndex >= len || data[startIndex] !== "{") {
    return { jsonStr: null, isIncomplete: false };
  }

  let braceCount = 0;
  let inString = false;
  let escapeNext = false;

  for (let i = startIndex; i < len; i++) {
    const char = data[i];

    if (escapeNext) {
      escapeNext = false;
      continue;
    }

    if (char === "\\") {
      escapeNext = true;
      continue;
    }

    if (char === '"') {
      inString = !inString;
      continue;
    }

    if (!inString) {
      if (char === "{") {
        braceCount++;
      } else if (char === "}") {
        braceCount--;
        
        // 找到完整的JSON对象
        if (braceCount === 0) {
          return {
            jsonStr: data.substring(startIndex, i + 1),
            isIncomplete: false,
          };
        }
      }
    }
  }

  // 没有找到完整的JSON对象，可能是数据被截断
  const isIncomplete = braceCount > 0;
  return { jsonStr: null, isIncomplete };
}

/**
 * 处理流式数据
 */
function processStreamData() {
  const parsedData = [];
  const buffer = incompleteBuffer;
  let processedLength = 0;

  // 第一步：处理可能的JSON续传数据（无data:前缀）
  if (!buffer.trim().startsWith("data:")) {
    const result = tryParseJsonFromStart(buffer.trim());
    if (result.jsonStr) {
      // 找到完整的续传JSON
      const parsed = tryParseJson(result.jsonStr);
      if (parsed) {
        parsedData.push(parsed);
      }

      // 计算处理的长度（包括前面的空白字符）
      const trimmedStart = buffer.length - buffer.trim().length;
      processedLength = trimmedStart + result.jsonStr.length;

      // 跳过JSON后的空白字符和换行符
      while (
        processedLength < buffer.length &&
        /\s/.test(buffer[processedLength])
      ) {
        processedLength++;
      }
    } else if (result.isIncomplete) {
      // JSON不完整，等待更多数据
      return {
        parsedData,
        remainingBuffer: buffer,
      };
    } else {
      // 不是有效的JSON，跳过到第一个data:或结束
      const firstDataIndex = buffer.indexOf("data:");
      if (firstDataIndex !== -1) {
        processedLength = firstDataIndex;
      } else {
        // 没有data:，全部丢弃
        return {
          parsedData,
          remainingBuffer: "",
        };
      }
    }
  }

  // 第二步：循环处理所有完整的data:块
  while (processedLength < buffer.length) {
    const remainingBuffer = buffer.substring(processedLength);

    // 查找下一个data:
    const dataMatch = remainingBuffer.match(/^\s*data:\s*/);
    if (!dataMatch) {
      // 没有找到data:，结束处理
      break;
    }

    // 找到data:，定位JSON开始位置
    const dataPrefix = dataMatch[0];
    const jsonStart = processedLength + dataPrefix.length;

    if (jsonStart >= buffer.length) {
      // data:后面没有内容，等待更多数据
      break;
    }

    if (buffer[jsonStart] !== "{") {
      // data:后面不是JSON开始，可能还在等待JSON数据
      break;
    }

    // 尝试提取完整的JSON对象
    const result = extractCompleteJsonFromPosition(buffer, jsonStart);
    
    if (result.jsonStr) {
      // 找到完整的JSON
      const parsed = tryParseJson(result.jsonStr);
      if (parsed) {
        parsedData.push(parsed);
      }
      processedLength = jsonStart + result.jsonStr.length;

      // 跳过JSON后的空白字符和换行符
      while (
        processedLength < buffer.length &&
        /\s/.test(buffer[processedLength])
      ) {
        processedLength++;
      }
    } else if (result.isIncomplete) {
      // JSON不完整，等待更多数据
      break;
    } else {
      // JSON格式错误，跳过这个data:
      processedLength = jsonStart;
    }
  }

  return {
    parsedData,
    remainingBuffer: buffer.substring(processedLength),
  };
}

function parseStreamingData(data) {
  const parsedData = [];

  // 将新数据转换为字节数组
  const newBytes = new TextEncoder().encode(data);
  
  // 合并之前的不完整字节和新字节
  const combinedBytes = new Uint8Array(incompleteBytes.length + newBytes.length);
  combinedBytes.set(incompleteBytes);
  combinedBytes.set(newBytes, incompleteBytes.length);
  
  // 安全解码合并后的字节
  const { decoded, remainingBytes } = safeDecodeBytes(combinedBytes);
  
  // 更新不完整字节缓存
  incompleteBytes = remainingBytes;
  
  if (decoded) {
    // 将解码后的数据追加到字符串缓存中
    incompleteBuffer += decoded;

    console.log("当前缓存内容:", incompleteBuffer);
    console.log("剩余字节数:", incompleteBytes.length);

    // 处理缓存中的数据
    const processResult = processStreamData();
    parsedData.push(...processResult.parsedData);

    // 更新缓存
    incompleteBuffer = processResult.remainingBuffer;

    console.log("解析到的数据条数:", parsedData.length);
    console.log("剩余缓存长度:", incompleteBuffer.length);
    console.log("剩余缓存内容:", incompleteBuffer);
  } else {
    console.log("当前数据无法解码，等待更多字节");
  }

  return parsedData;
}

/**
 * 重置解析器的内部缓存
 */
function resetStreamingParser() {
  incompleteBuffer = "";
  incompleteBytes = new Uint8Array(0);
}

// 测试字节级截断处理
function testByteLevelTruncation() {
  console.log("=== 测试字节级截断处理 ===");
  
  resetStreamingParser();
  
  const json1 = '{"type": "start", "content": "开始消息"}';
  const json2 = '{"type": "middle", "content": "中间消息"}';
  const json3 = '{"type": "end", "content": "结束消息"}';
  
  const fullData = `data: ${json1}\ndata: ${json2}\ndata: ${json3}`;
  console.log("完整数据:", fullData);
  
  const fullBytes = new TextEncoder().encode(fullData);
  console.log("总字节数:", fullBytes.length);
  
  // 模拟在中文字符中间被截断
  const cutPos1 = 35; // 在第一个JSON的中文字符中间截断
  const cutPos2 = 80; // 在第二个JSON的某个位置截断
  
  const chunk1Bytes = fullBytes.slice(0, cutPos1);
  const chunk2Bytes = fullBytes.slice(cutPos1, cutPos2);
  const chunk3Bytes = fullBytes.slice(cutPos2);
  
  // 将字节转换为字符串（可能包含不完整字符）
  const chunk1 = new TextDecoder("utf-8", { fatal: false }).decode(chunk1Bytes);
  const chunk2 = new TextDecoder("utf-8", { fatal: false }).decode(chunk2Bytes);
  const chunk3 = new TextDecoder("utf-8", { fatal: false }).decode(chunk3Bytes);
  
  console.log("\nChunk1:", `"${chunk1}"`);
  console.log("Chunk1 包含替换字符:", chunk1.includes('\uFFFD'));
  
  console.log("\nChunk2:", `"${chunk2}"`);
  console.log("Chunk2 包含替换字符:", chunk2.includes('\uFFFD'));
  
  console.log("\nChunk3:", `"${chunk3}"`);
  console.log("Chunk3 包含替换字符:", chunk3.includes('\uFFFD'));
  
  // 使用新的字节级处理逻辑
  console.log("\n=== 使用字节级处理 ===");
  
  const result1 = parseStreamingData(chunk1);
  console.log("Result1 长度:", result1.length);
  
  const result2 = parseStreamingData(chunk2);
  console.log("Result2 长度:", result2.length);
  
  const result3 = parseStreamingData(chunk3);
  console.log("Result3 长度:", result3.length);
  console.log("Result3 内容:", result3);
  
  console.log("\n期望结果: 0, 0, 3");
  console.log("实际结果:", result1.length, result2.length, result3.length);
  
  const success = result1.length === 0 && result2.length === 0 && result3.length === 3;
  console.log(success ? "✅ 字节级处理测试通过" : "❌ 字节级处理测试失败");
  
  return success;
}

testByteLevelTruncation();
