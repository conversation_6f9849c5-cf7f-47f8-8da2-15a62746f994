// 测试具体的字符截断问题："冷冻去骨猪肉�ע"

/**
 * 检查字符串是否包含无效字符
 */
function hasInvalidCharacters(str) {
  // 检查替换字符
  if (str.includes('\uFFFD')) {
    return true;
  }
  
  // 检查其他可能的无效字符模式
  // 希伯来字符在中文上下文中通常是错误的
  const hebrewPattern = /[\u0590-\u05FF]/;
  if (hebrewPattern.test(str)) {
    console.log("检测到可能的字符编码错误（希伯来字符）");
    return true;
  }
  
  // 检查其他异常字符组合
  const arabicPattern = /[\u0600-\u06FF]/;
  if (arabicPattern.test(str)) {
    console.log("检测到可能的字符编码错误（阿拉伯字符）");
    return true;
  }
  
  return false;
}

/**
 * 从字节数组安全解码字符串
 */
function safeDecodeBytes(bytes) {
  if (bytes.length === 0) {
    return { decoded: "", remainingBytes: new Uint8Array(0) };
  }

  // 尝试解码整个字节数组
  try {
    const decoded = new TextDecoder("utf-8", { fatal: true }).decode(bytes);
    
    // 检查解码结果是否包含无效字符
    if (hasInvalidCharacters(decoded)) {
      console.log("解码结果包含无效字符，尝试安全截断");
      throw new Error("Invalid characters detected");
    }
    
    return { decoded, remainingBytes: new Uint8Array(0) };
  } catch {
    // 解码失败，从末尾开始尝试找到安全的解码位置
    for (let i = bytes.length - 1; i >= Math.max(0, bytes.length - 6); i--) {
      try {
        const safeBytes = bytes.slice(0, i);
        const decoded = new TextDecoder("utf-8", { fatal: true }).decode(safeBytes);
        
        // 检查部分解码结果是否包含无效字符
        if (hasInvalidCharacters(decoded)) {
          continue;
        }
        
        const remainingBytes = bytes.slice(i);
        console.log(
          "字节级安全解码，安全长度:",
          i,
          "剩余字节:",
          remainingBytes.length,
          "解码内容:",
          decoded.substring(Math.max(0, decoded.length - 20))
        );
        return { decoded, remainingBytes };
      } catch {
        continue;
      }
    }

    // 如果都无法解码，检查是否整个字节数组都是无效的
    console.warn(
      "无法安全解码字节数组，长度:",
      bytes.length,
      "字节内容:",
      Array.from(bytes.slice(0, 10))
    );
    
    // 尝试非严格解码来诊断问题
    try {
      const nonStrictDecoded = new TextDecoder("utf-8", { fatal: false }).decode(bytes);
      console.log("非严格解码结果:", nonStrictDecoded);
      if (hasInvalidCharacters(nonStrictDecoded)) {
        console.log("非严格解码也包含无效字符，丢弃这些字节");
        return { decoded: "", remainingBytes: new Uint8Array(0) };
      }
    } catch (e) {
      console.log("非严格解码也失败:", e);
    }
    
    return { decoded: "", remainingBytes: bytes };
  }
}

// 测试具体的问题字符串
function testSpecificIssue() {
  console.log("=== 测试具体问题：冷冻去骨猪肉�ע ===");
  
  const problematicString = "冷冻去骨猪肉�ע";
  console.log("问题字符串:", `"${problematicString}"`);
  console.log("字符串长度:", problematicString.length);
  
  // 分析字符串中的每个字符
  console.log("\n字符分析:");
  for (let i = 0; i < problematicString.length; i++) {
    const char = problematicString[i];
    const charCode = char.charCodeAt(0);
    console.log(`位置 ${i}: "${char}" (U+${charCode.toString(16).toUpperCase().padStart(4, '0')})`);
  }
  
  // 检查是否包含无效字符
  const hasInvalid = hasInvalidCharacters(problematicString);
  console.log("\n包含无效字符:", hasInvalid);
  
  // 尝试将字符串转换为字节再解码
  console.log("\n=== 字节转换测试 ===");
  const bytes = new TextEncoder().encode(problematicString);
  console.log("字节数组:", Array.from(bytes));
  console.log("字节长度:", bytes.length);
  
  // 使用安全解码函数
  const result = safeDecodeBytes(bytes);
  console.log("安全解码结果:", `"${result.decoded}"`);
  console.log("剩余字节:", result.remainingBytes.length);
  
  return result;
}

// 模拟正确的字符串应该是什么样的
function testCorrectString() {
  console.log("\n=== 测试正确的字符串 ===");
  
  const correctString = "冷冻去骨猪肉";
  console.log("正确字符串:", `"${correctString}"`);
  
  const bytes = new TextEncoder().encode(correctString);
  console.log("正确字符串字节:", Array.from(bytes));
  
  const result = safeDecodeBytes(bytes);
  console.log("正确字符串解码:", `"${result.decoded}"`);
  
  return result;
}

// 模拟字符截断和恢复过程
function simulateCharacterTruncationRecovery() {
  console.log("\n=== 模拟字符截断和恢复 ===");
  
  const originalString = '{"content": "冷冻去骨猪肉"}';
  console.log("原始字符串:", originalString);
  
  const bytes = new TextEncoder().encode(originalString);
  console.log("原始字节数:", bytes.length);
  
  // 在"肉"字符中间截断
  const meatIndex = originalString.indexOf("肉");
  console.log("'肉'字符位置:", meatIndex);
  
  // 获取"肉"字符的字节
  const beforeMeat = originalString.substring(0, meatIndex);
  const beforeBytes = new TextEncoder().encode(beforeMeat);
  const meatBytes = new TextEncoder().encode("肉");
  
  console.log("'肉'字符字节:", Array.from(meatBytes));
  
  // 在"肉"字符的第2个字节处截断
  const cutPos = beforeBytes.length + 2;
  
  const chunk1Bytes = bytes.slice(0, cutPos);
  const chunk2Bytes = bytes.slice(cutPos);
  
  console.log("Chunk1 字节:", Array.from(chunk1Bytes));
  console.log("Chunk2 字节:", Array.from(chunk2Bytes));
  
  // 模拟字节级处理
  let incompleteBytes = new Uint8Array(0);
  
  // 处理第一个chunk
  console.log("\n处理Chunk1:");
  let combinedBytes = new Uint8Array(incompleteBytes.length + chunk1Bytes.length);
  combinedBytes.set(incompleteBytes);
  combinedBytes.set(chunk1Bytes, incompleteBytes.length);
  
  let result1 = safeDecodeBytes(combinedBytes);
  incompleteBytes = result1.remainingBytes;
  
  console.log("Chunk1 解码:", `"${result1.decoded}"`);
  console.log("剩余字节:", incompleteBytes.length);
  
  // 处理第二个chunk
  console.log("\n处理Chunk2:");
  combinedBytes = new Uint8Array(incompleteBytes.length + chunk2Bytes.length);
  combinedBytes.set(incompleteBytes);
  combinedBytes.set(chunk2Bytes, incompleteBytes.length);
  
  let result2 = safeDecodeBytes(combinedBytes);
  
  console.log("Chunk2 解码:", `"${result2.decoded}"`);
  
  // 合并结果
  const finalResult = result1.decoded + result2.decoded;
  console.log("最终合并结果:", `"${finalResult}"`);
  console.log("与原始字符串一致:", finalResult === originalString);
  
  return finalResult === originalString;
}

const result1 = testSpecificIssue();
const result2 = testCorrectString();
const result3 = simulateCharacterTruncationRecovery();

console.log("\n=== 总结 ===");
console.log("问题字符串处理:", result1.decoded ? "✅ 已清理" : "❌ 仍有问题");
console.log("正确字符串处理:", result2.decoded ? "✅ 正常" : "❌ 异常");
console.log("截断恢复测试:", result3 ? "✅ 成功" : "❌ 失败");

if (result1.decoded !== result1.decoded && hasInvalidCharacters(result1.decoded)) {
  console.log("\n🔧 建议：当检测到无效字符时，应该丢弃或重新请求数据");
}
