import { parseStreamingData, resetStreamingParser } from "./useParseStreaming";

// 简单测试场景三
console.log("=== 简单测试场景三 ===");

function testScenario3() {
  resetStreamingParser();
  
  const json1 = '{"type": "start"}';
  const json2 = '{"type": "middle"}';
  const json3 = '{"type": "end"}';
  
  console.log("JSON1:", json1);
  console.log("JSON2:", json2);
  console.log("JSON3:", json3);
  
  // 第一次：data: + JSON开始部分
  const chunk1 = "data: " + json1.substring(0, 10); // "data: {"type":"
  console.log("\nChunk1:", `"${chunk1}"`);
  
  const result1 = parseStreamingData(chunk1);
  console.log("Result1:", result1.length, result1);
  
  // 第二次：JSON中间部分
  const chunk2 = json1.substring(10, 15); // "start"
  console.log("\nChunk2:", `"${chunk2}"`);
  
  const result2 = parseStreamingData(chunk2);
  console.log("Result2:", result2.length, result2);
  
  // 第三次：JSON结尾 + 完整data块
  const chunk3 = json1.substring(15) + `\ndata: ${json2}\ndata: ${json3}`; // "} + data: {...} + data: {...}"
  console.log("\nChunk3:", `"${chunk3}"`);
  
  const result3 = parseStreamingData(chunk3);
  console.log("Result3:", result3.length, result3);
  
  console.log("\n期望: 0, 0, 3");
  console.log("实际:", result1.length, result2.length, result3.length);
  
  return result3.length === 3;
}

// 测试更简单的情况
function testSimpleCase() {
  resetStreamingParser();
  
  console.log("\n=== 测试更简单的情况 ===");
  
  // 直接测试一个完整的缓存
  const json1 = '{"a":1}';
  const json2 = '{"b":2}';
  
  const completeBuffer = `data: ${json1}\ndata: ${json2}`;
  console.log("完整缓存:", `"${completeBuffer}"`);
  
  const result = parseStreamingData(completeBuffer);
  console.log("结果:", result.length, result);
  
  return result.length === 2;
}

const test1 = testScenario3();
const test2 = testSimpleCase();

console.log("\n=== 测试结果 ===");
console.log("场景三测试:", test1 ? "✅ 通过" : "❌ 失败");
console.log("简单测试:", test2 ? "✅ 通过" : "❌ 失败");
