// 测试字符截断检测功能

/**
 * 检测字符串是否在多字节字符边界被截断
 */
function detectTruncatedCharacter(str) {
  if (!str) return -1;
  
  try {
    // 检查字符串末尾是否有不完整的UTF-8字符
    const bytes = new TextEncoder().encode(str);
    const decoded = new TextDecoder("utf-8", { fatal: true }).decode(bytes);
    
    // 如果解码成功且内容一致，说明没有截断
    if (decoded === str) {
      return -1;
    }
  } catch {
    // 解码失败，说明有截断，从末尾开始查找安全位置
    for (let i = str.length - 1; i >= Math.max(0, str.length - 4); i--) {
      try {
        const testStr = str.substring(0, i);
        const testBytes = new TextEncoder().encode(testStr);
        const testDecoded = new TextDecoder("utf-8", { fatal: true }).decode(testBytes);
        if (testDecoded === testStr) {
          console.log("检测到字符截断，安全位置:", i, "原长度:", str.length);
          return i; // 找到安全的截断位置
        }
      } catch {
        continue;
      }
    }
  }
  
  return -1;
}

// 模拟字符截断的情况
function simulateCharacterTruncation() {
  console.log("=== 测试字符截断检测 ===");
  
  // 测试1: 正常字符串（无截断）
  const normalStr = 'data: {"content": "hello"}';
  const result1 = detectTruncatedCharacter(normalStr);
  console.log("测试1 - 正常字符串:", result1 === -1 ? "✅ 通过" : "❌ 失败");
  
  // 测试2: 包含中文的正常字符串
  const chineseStr = 'data: {"content": "你好世界"}';
  const result2 = detectTruncatedCharacter(chineseStr);
  console.log("测试2 - 中文字符串:", result2 === -1 ? "✅ 通过" : "❌ 失败");
  
  // 测试3: 模拟中文字符被截断
  const fullChinese = 'data: {"content": "你好"}';
  const bytes = new TextEncoder().encode(fullChinese);
  
  // 在中文字符中间截断（"你"字符的第2个字节）
  const truncatedBytes = bytes.slice(0, bytes.length - 2); // 去掉最后2个字节
  const truncatedStr = new TextDecoder("utf-8", { ignoreBOM: true, fatal: false }).decode(truncatedBytes);
  
  console.log("原始字符串:", fullChinese);
  console.log("截断后字符串:", `"${truncatedStr}"`);
  console.log("截断后字符串长度:", truncatedStr.length);
  
  const result3 = detectTruncatedCharacter(truncatedStr);
  console.log("测试3 - 截断检测:", result3 > 0 ? "✅ 检测到截断" : "❌ 未检测到截断");
  
  if (result3 > 0) {
    const safeStr = truncatedStr.substring(0, result3);
    console.log("安全部分:", `"${safeStr}"`);
    console.log("剩余部分:", `"${truncatedStr.substring(result3)}"`);
  }
  
  // 测试4: 模拟实际的流式数据截断场景
  console.log("\n=== 模拟实际流式数据截断 ===");
  
  const realData = 'data: {"type": "message", "content": "这是一条包含中文的消息"}';
  const realBytes = new TextEncoder().encode(realData);
  
  // 在不同位置截断
  for (let cutPos of [realBytes.length - 1, realBytes.length - 2, realBytes.length - 3]) {
    const cutBytes = realBytes.slice(0, cutPos);
    const cutStr = new TextDecoder("utf-8", { ignoreBOM: true, fatal: false }).decode(cutBytes);
    
    console.log(`\n截断位置 ${cutPos}:`);
    console.log("截断字符串:", `"${cutStr}"`);
    
    const detectResult = detectTruncatedCharacter(cutStr);
    if (detectResult > 0) {
      console.log("✅ 检测到截断，安全位置:", detectResult);
      const safePart = cutStr.substring(0, detectResult);
      const remainPart = cutStr.substring(detectResult);
      console.log("安全部分:", `"${safePart}"`);
      console.log("剩余部分:", `"${remainPart}"`);
    } else {
      console.log("❌ 未检测到截断");
    }
  }
}

simulateCharacterTruncation();
